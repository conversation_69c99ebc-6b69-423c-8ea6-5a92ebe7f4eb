/* ========================================
   UI COMPONENTS.JS - UI population and display components
   Handles UI population, card info display, and image display
   ======================================== */

// ========================================
// UI POPULATION FUNCTIONS
// ========================================

// Populate card information - Điền thông tin card vào UI (không tự trigger animation)
function populateCardInfo(cardInfo) {
    if (!cardInfo || Object.keys(cardInfo).length === 0) {
        return false;
    }

    const fieldMappings = {
        'resultName': ['name', 'full_name', 'Name', 'Full_Name'],
        'resultTitle': ['title', 'position', 'job_title', 'Title', 'Position'],
        'resultCompany': ['company', 'organization', 'Company', 'Organization'],
        'resultEmail': ['email', 'Email'],
        'resultPhone': ['phone', 'mobile', 'Phone', 'Mobile'],
        'resultAddress': ['address', 'location', 'Address', 'Location']
    };

    Object.keys(fieldMappings).forEach(elementId => {
        const element = document.getElementById(elementId);
        if (element) {
            let value = null;

            for (const fieldName of fieldMappings[elementId]) {
                if (cardInfo[fieldName]) {
                    value = cardInfo[fieldName];
                    break;
                }
            }

            element.textContent = value || 'No data yet';
        }
    });

    return true;
}

// Populate generated images - Điền ảnh AI đã tạo vào UI
function populateGeneratedImages(generatedImages) {
    const container = document.getElementById('generatedImagesDisplay');
    if (!container) {
        return false;
    }

    if (!generatedImages || generatedImages.length === 0) {
        container.innerHTML = `
            <div class="empty-state">
                <div class="empty-state-icon">🖼️</div>
                <p>Chưa có ảnh AI được tạo</p>
            </div>
        `;
        return false;
    }

    container.innerHTML = '';

    generatedImages.forEach((imageInfo, index) => {
        const imageItem = document.createElement('div');
        imageItem.className = 'image-item';

        let imagePath;
        if (typeof imageInfo === 'string') {
            imagePath = imageInfo;
        } else if (imageInfo.image_path) {
            imagePath = imageInfo.image_path;
        } else if (imageInfo.path) {
            imagePath = imageInfo.path;
        } else {
            imagePath = imageInfo;
        }

        const filename = `ai_image_${index + 1}.jpg`;

        imageItem.innerHTML = `
            <div class="image-display">
                <img src="/${imagePath}" alt="Generated AI Image ${index + 1}" loading="lazy"
                     onerror="console.error('Failed to load image: ${imagePath}')">
                <div class="image-overlay">
                    AI Generated Image ${index + 1}
                </div>
            </div>
            <div class="image-actions">
                <a href="/${imagePath}" download="${filename}" class="download-btn">
                    📥 Tải về
                </a>
            </div>
        `;

        container.appendChild(imageItem);
    });

    return true;
}

// ========================================
// BACKEND API INTEGRATION FUNCTIONS
// ========================================

// Show loading state for card info
function showCardInfoLoading() {
    console.log('⏳ Showing loading state for card info...');
    const fields = ['cardName', 'cardTitle', 'cardCompany', 'cardEmail', 'cardPhone', 'cardAddress'];
    fields.forEach(fieldId => {
        const element = document.getElementById(fieldId);
        if (element) {
            element.textContent = 'Đang tải...';
            element.style.color = 'var(--color-text-secondary)';
            element.style.fontStyle = 'italic';
        }
    });
}

// Update card information display with OCR results
function updateCardInfoDisplay(ocrData) {
    console.log('📄 Updating card info display with OCR data:', ocrData);

    // Update individual fields with fallback values and loading states
    const updateField = (fieldId, value, fallback = 'Không có dữ liệu') => {
        const element = document.getElementById(fieldId);
        if (element) {
            if (value === 'loading') {
                element.textContent = 'Đang tải...';
                element.style.color = 'var(--color-text-secondary)';
                element.style.fontStyle = 'italic';
            } else {
                element.textContent = value || fallback;
                element.style.color = value ? 'var(--color-text-primary)' : 'var(--color-text-secondary)';
                element.style.fontStyle = 'normal';
            }
        }
    };

    // Update all card fields
    updateField('cardName', ocrData.name || ocrData.full_name);
    updateField('cardTitle', ocrData.title || ocrData.position);
    updateField('cardCompany', ocrData.company || ocrData.organization);
    updateField('cardEmail', ocrData.email);
    updateField('cardPhone', ocrData.phone || ocrData.mobile);
    updateField('cardAddress', ocrData.address || ocrData.location);

    // Show OCR results section instead of old results
    showOCRResultsSection(ocrData);

    console.log('✅ Card info display updated successfully');
}

// Show OCR results overlay and hide camera streams
function showOCRResultsSection(ocrData) {

    const cameraContainers = document.querySelectorAll('.camera-container');
    cameraContainers.forEach(container => {
        container.style.opacity = '0.1';
        container.style.pointerEvents = 'none';
    });

    const resultsSection = document.getElementById('ocrResultsSection');
    if (resultsSection) {
        resultsSection.style.display = 'block';

        const updateResultField = (fieldId, value) => {
            const element = document.getElementById(fieldId);
            if (element) {
                element.textContent = value || '—';
            }
        };

        updateResultField('resultName', ocrData.name || ocrData.full_name);
        updateResultField('resultTitle', ocrData.title || ocrData.position);
        updateResultField('resultCompany', ocrData.company || ocrData.organization);
        updateResultField('resultEmail', ocrData.email);
        updateResultField('resultPhone', ocrData.phone || ocrData.mobile);
        updateResultField('resultAddress', ocrData.address || ocrData.location);
    }
}

function hideOCRResultsSection() {
    const cameraContainers = document.querySelectorAll('.camera-container');
    cameraContainers.forEach(container => {
        container.style.opacity = '1';
        container.style.pointerEvents = 'auto';
    });

    const resultsSection = document.getElementById('ocrResultsSection');
    if (resultsSection) {
        resultsSection.style.display = 'none';
    }
}

function showGeneratedImagesOverlay(generatedImages) {
    const ocrSection = document.getElementById('ocrResultsSection');
    if (ocrSection) {
        ocrSection.style.display = 'none';
    }

    const imagesOverlay = document.getElementById('generatedImagesOverlay');
    if (imagesOverlay && generatedImages && generatedImages.length > 0) {
        imagesOverlay.style.display = 'block';

        // Chỉ hiển thị 1 ảnh AI
        const img1 = document.getElementById('generatedImage1');
        if (img1 && generatedImages[0]) {
            // Handle different image path formats
            let imagePath;
            if (typeof generatedImages[0] === 'string') {
                imagePath = generatedImages[0];
            } else if (generatedImages[0].url) {
                imagePath = generatedImages[0].url;
            } else if (generatedImages[0].path) {
                imagePath = generatedImages[0].path;
            } else if (generatedImages[0].image_path) {
                imagePath = generatedImages[0].image_path;
            } else {
                imagePath = String(generatedImages[0]);
            }
            
            img1.src = `/${imagePath}`;
            img1.alt = generatedImages[0].description || 'Generated AI Image';
            
            // Add error handling
            img1.onerror = () => {
                console.error('❌ Failed to load generated image:', imagePath);
                img1.src = '/static/img/placeholder.png'; // Fallback image
            };
            
            img1.onload = () => {
                console.log('✅ Generated image loaded successfully:', imagePath);
            };
        }
    } else {
        console.warn('⚠️ No generated images to display or overlay not found');
    }
}

// Hide generated images overlay and show camera streams (for auto reset)
function hideGeneratedImagesOverlay() {
    const cameraContainers = document.querySelectorAll('.camera-container');
    cameraContainers.forEach(container => {
        container.style.opacity = '1';
        container.style.pointerEvents = 'auto';
    });

    const imagesOverlay = document.getElementById('generatedImagesOverlay');
    if (imagesOverlay) {
        imagesOverlay.style.display = 'none';
    }
}

// Auto transition from OCR to Generated Images after delay
function autoTransitionToGeneratedImages(generatedImages, delay = 3000) {
    setTimeout(() => {
        showGeneratedImagesOverlay(generatedImages);
    }, delay);
}

// Update generated images display
function updateGeneratedImagesDisplay(imageData) {
    console.log('🖼️ Updating generated images display:', imageData);

    const imagesContainer = document.getElementById('generatedImagesDisplay');
    if (!imagesContainer) {
        console.error('❌ Images container not found');
        return;
    }

    // Clear existing content
    imagesContainer.innerHTML = '';

    if (imageData && imageData.images && imageData.images.length > 0) {
        imageData.images.forEach((imageInfo, index) => {
            // FIXED: Handle different image path formats properly
            let imagePath;
            if (typeof imageInfo === 'string') {
                imagePath = imageInfo;
            } else if (imageInfo && imageInfo.image_path) {
                imagePath = imageInfo.image_path;
            } else if (imageInfo && imageInfo.path) {
                imagePath = imageInfo.path;
            } else if (imageInfo && imageInfo.url) {
                imagePath = imageInfo.url;
            } else {
                console.warn('⚠️ Unknown image format:', imageInfo);
                imagePath = String(imageInfo); // Convert to string as fallback
            }

            console.log(`🖼️ Processing image ${index + 1}: ${imagePath}`);

            const imageElement = document.createElement('div');
            imageElement.className = 'generated-image-item';
            imageElement.innerHTML = `
                <img src="/${imagePath}" alt="Generated Image ${index + 1}" class="generated-image">
                <div class="image-actions">
                    <button class="apple-button apple-button-secondary" onclick="downloadImage('/${imagePath}', 'generated_image_${index + 1}')">
                        <span>💾</span> Tải xuống
                    </button>
                </div>
            `;
            imagesContainer.appendChild(imageElement);
        });
        console.log(`✅ ${imageData.images.length} images displayed successfully`);
    } else {
        imagesContainer.innerHTML = `
            <div class="empty-state">
                <p>Chưa có ảnh được tạo</p>
            </div>
        `;
        console.log('ℹ️ No images to display');
    }
}

// NEW FUNCTION: Display workflow results from combined API
function displayWorkflowResults(data) {
    console.log('📊 Displaying workflow results:', data);

    try {
        // COMMENTED OUT: Old results section display logic - không sử dụng resultsSection nữa
        /*
        // Enable scrolling first to allow viewing results
        document.body.classList.remove('no-scroll');
        document.body.classList.add('allow-scroll');
        console.log('✅ Scrolling enabled for results viewing');

        // Also call the function if available
        if (typeof setPageScrolling === 'function') {
            setPageScrolling(true);
        }

        // Force show results section first
        const resultsSection = document.getElementById('resultsSection');
        if (resultsSection) {
            resultsSection.classList.remove('section-hidden');
            resultsSection.classList.add('section-visible');
        }

        // Display card info if available
        if (data.card_info && Object.keys(data.card_info).length > 0) {
            populateCardInfo(data.card_info);

            // Force show card info section
            const cardSection = document.getElementById('cardInfoSection');
            if (cardSection) {
                cardSection.classList.remove('section-hidden');
                cardSection.classList.add('section-visible', 'slide-in');
            }
        }

        // Display generated images if available
        if (data.generated_images && data.generated_images.length > 0) {
            populateGeneratedImages(data.generated_images);

            // Force show images section
            const imagesSection = document.getElementById('imagesSection');
            if (imagesSection) {
                imagesSection.classList.remove('section-hidden');
                imagesSection.classList.add('section-visible', 'slide-in');
            }

            // Show "Create New Images" button when images are successfully generated
            const createBtn = document.getElementById('createNewImagesBtn');
            if (createBtn) {
                createBtn.style.display = 'inline-block';
            }
        }

        // Auto-scroll to results section after a short delay
        setTimeout(() => {
            const resultsSection = document.getElementById('resultsSection');
            if (resultsSection) {
                resultsSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
                console.log('📍 Auto-scrolled to results section');
            }
        }, 1000); // Wait 1 second for animations to complete
        */

        // Auto-play TTS if available (commented out to avoid duplicate playback)
        // Audio is already handled by fetchOCRResults function
        /*
        if (data.audio_file) {
            if (typeof playTTSAudio === 'function') {
                playTTSAudio(data.audio_file);
            }
        }
        */

        // Auto transition to generated images if available
        if (data.generated_images && data.generated_images.length > 0) {
            autoTransitionToGeneratedImages(data.generated_images, 3000); // 3 seconds after OCR
        }

        if (data && data.workflow_completed && typeof startAutoResetWithLoading === 'function') {
            console.log('🔄 Scheduling auto-reset in 8 seconds...');
            setTimeout(() => {
                console.log('🔄 Triggering auto-reset now...');
                startAutoResetWithLoading();
            }, 8000); // Longer delay for images
        } else {
            console.warn('⚠️ Auto-reset not scheduled:', {
                workflow_completed: data?.workflow_completed,
                startAutoResetWithLoading: typeof startAutoResetWithLoading
            });
        }

    } catch (error) {
        console.error('❌ Error in displayWorkflowResults:', error);
        if (typeof showMessage === 'function') {
            showMessage('⚠️ Có lỗi hiển thị kết quả. Vui lòng refresh trang.', 'warning');
        }
    }


}

// ========================================
// DOCUMENT READY & INITIALIZATION
// ========================================

// Document ready event - Khởi tạo khi trang web load xong
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 UI Components JavaScript loaded successfully!');
    console.log('📱 User Agent:', navigator.userAgent);
    console.log('📏 Screen size:', window.screen.width + 'x' + window.screen.height);
    console.log('🖥️ Viewport size:', window.innerWidth + 'x' + window.innerHeight);



    // Track workflow status
    if (typeof trackWorkflowStatus === 'function') {
        trackWorkflowStatus();
    }

    // Vô hiệu hóa scroll ban đầu
    if (typeof setPageScrolling === 'function') {
        setPageScrolling(false);
    }
});

// ========================================
// EXPORT FUNCTIONS TO GLOBAL SCOPE
// ========================================

// Export functions to global scope
window.displayWorkflowResults = displayWorkflowResults;
window.populateCardInfo = populateCardInfo;
window.populateGeneratedImages = populateGeneratedImages;
window.showCardInfoLoading = showCardInfoLoading;
window.updateCardInfoDisplay = updateCardInfoDisplay;
window.updateGeneratedImagesDisplay = updateGeneratedImagesDisplay;
window.showOCRResultsSection = showOCRResultsSection;
window.hideOCRResultsSection = hideOCRResultsSection;
window.showGeneratedImagesOverlay = showGeneratedImagesOverlay;
window.hideGeneratedImagesOverlay = hideGeneratedImagesOverlay;
window.autoTransitionToGeneratedImages = autoTransitionToGeneratedImages;

// Test function for auto-reset
window.testAutoReset = function() {
    console.log('🧪 Testing auto-reset manually...');
    if (typeof startAutoResetWithLoading === 'function') {
        startAutoResetWithLoading();
    } else {
        console.error('❌ startAutoResetWithLoading function not available');
    }
};
