/* ========================================
   CAMERA STYLES CSS - Camera interface and controls
   Styles for camera grid, containers, and video streams
   Includes Apple Design System camera enhancements
   ======================================== */

/* ===== ENHANCED CAMERA INTERFACE ===== */
.camera-main-container {
  background: var(--bg-card);
  border-radius: var(--radius-3xl);
  padding: 0 !important;                       /* NO PADDING for true full width */
  box-shadow: 0 20px 60px rgba(102, 126, 234, 0.3);
  border: 3px solid rgba(255,255,255,0.4);
  backdrop-filter: blur(30px);
  width: 100vw !important;                     /* VIEWPORT WIDTH - absolute full screen */
  margin: 0 !important;                        /* No margins at all */
  box-sizing: border-box !important;           /* Include borders in width calculation */
}

.camera-section-header {
  margin-bottom: var(--spacing-xl);
}

.camera-container-enhanced:hover {
  transform: translateY(-4px);
  box-shadow: 0 15px 40px rgba(102, 126, 234, 0.2);
  border-color: rgba(255,255,255,0.4);
}

.camera-header-enhanced {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-md);
  border-bottom: 2px solid rgba(255,255,255,0.1);
}

.camera-title-section {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.camera-title-text h3 {
  font-size: 30px;
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
  margin-bottom: 4px;
}

.camera-title-text p {
  font-size: 30px;
  color: var(--color-text-secondary);
  margin: 0;
}

.status-indicator-enhanced {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  background: rgba(255,255,255,0.1);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-md);
  backdrop-filter: blur(10px);
}

.camera-stream-enhanced {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

/* Support both img and video elements */
.camera-stream-enhanced video,
.camera-stream-enhanced img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

/* CAMERA SECTION STYLES */

/* Camera section - CONTRASTING BACKGROUND for visual separation */
#cameraSection {
    background: linear-gradient(135deg,
        rgba(30, 41, 59, 0.95) 0%,       /* Dark slate */
        rgba(51, 65, 85, 0.92) 25%,      /* Slate gray */
        rgba(30, 41, 59, 0.95) 50%,      /* Dark slate */
        rgba(51, 65, 85, 0.92) 75%,      /* Slate gray */
        rgba(30, 41, 59, 0.95) 100%      /* Dark slate */
    ) !important;
    border: 3px solid rgba(102, 126, 234, 0.6);  /* Bright purple border for contrast */
    position: relative;                           /* Để thêm overlay nếu cần */
    box-shadow: 0 15px 50px rgba(0, 0, 0, 0.3);  /* Dark shadow for depth */
}

/* Camera section overlay - Enhanced contrast texture */
#cameraSection::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 20%,
        rgba(102, 126, 234, 0.15) 0%,
        rgba(67, 56, 202, 0.1) 30%,
        transparent 60%
    );
    pointer-events: none;                         /* Không ảnh hưởng đến tương tác */
    border-radius: inherit;                       /* Kế thừa border-radius */
}

/* Camera section text contrast - Đảm bảo text rõ ràng trên gradient */
#cameraSection .camera-title,
#cameraSection .control-title,
#cameraSection .apple-select,
#cameraSection .apple-button {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);   /* Text shadow nhẹ */
}

#cameraSection .section-body {
    position: relative;                           /* Đảm bảo content nằm trên overlay */
    z-index: 1;                                  /* Z-index cao hơn overlay */
}

/* Camera grid - ABSOLUTE FULL WIDTH MAXIMUM SPACE */
.camera-grid {
    display: grid !important;                    /* CSS Grid */
    grid-template-columns: 1fr 1fr !important;  /* 2 cột bằng nhau hoàn toàn */
    gap: 4px !important;                         /* ABSOLUTE MINIMAL gap */
    margin: 0 !important;                        /* NO margins at all */
    align-items: stretch !important;             /* Căn đều chiều cao */
    width: 100vw !important;                     /* VIEWPORT WIDTH - absolute full screen */
    min-height: 500px !important;               /* MUCH TALLER cameras */
    padding: 0 !important;                       /* NO PADDING whatsoever */
    position: relative !important;              /* Để OCR overlay có thể đè lên */
    box-sizing: border-box !important;           /* Include borders in width calculation */
}

/* Camera container - MAXIMUM SPACE EFFICIENCY */
.camera-container {
    background: rgba(102, 126, 234, 0.08) !important; /* Background tím nhẹ */
    border-radius: 8px !important;               /* MINIMAL border radius */
    overflow: hidden !important;                 /* Ẩn nội dung tràn */
    border: 1px solid rgba(102, 126, 234, 0.25) !important; /* THINNER border */
    display: flex !important;                    /* Flexbox để kiểm soát layout */
    flex-direction: column !important;           /* Sắp xếp theo cột */
    height: 100% !important;                     /* Chiều cao 100% để cân đối */
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.1) !important; /* LIGHTER shadow */
    transition: all 0.3s ease !important;        /* Smooth transition */
}

.camera-container.captured {
    background: rgba(67, 233, 123, 0.1) !important; 
    box-shadow: 0 8px 25px rgba(67, 233, 123, 0.3) !important; 
    transform: translateY(-2px) !important;      
}

.camera-container.captured video,
.camera-container.captured img {
    border: 3px solid #43e97b !important;        
}

/* Camera header - Header của mỗi camera (BEAUTIFUL GRADIENT) */
.camera-header {
    background: linear-gradient(135deg,
        #667eea 0%,
        #764ba2 50%,
        #667eea 100%
    );                                          /* Gradient tím đẹp */
    padding: 6px 8px !important;                /* ULTRA MINIMAL padding */
    display: flex;                               /* Flexbox */
    justify-content: space-between;              /* Căn đều 2 đầu */
    align-items: center;                         /* Căn giữa theo chiều dọc */
    min-height: 35px !important;                /* ULTRA COMPACT height */
    flex-shrink: 0;                             /* Không co lại */
    color: white;                               /* Màu chữ trắng */
    font-weight: var(--font-weight-semibold);   /* Độ đậm cao */
    box-shadow: 0 2px 10px rgba(102, 126, 234, 0.3); /* Shadow header */
}

/* Camera info - Thông tin camera (icon + title) */
.camera-info {
    display: flex;                               /* Flexbox */
    align-items: center;                         /* Căn giữa theo chiều dọc */
    gap: var(--spacing-sm);                      /* Khoảng cách giữa icon và title */
    flex: 1;                                    /* Chiếm không gian còn lại */
}

/* Camera icon - Icon camera (emoji - deprecated) */
.camera-icon {
    font-size: 20px;                            /* Kích thước icon */
}

/* Camera icon image - MAXIMUM VISIBILITY and COMPACT */
.camera-icon-img {
    height: 28px !important;                    /* COMPACT icon size */
    width: 28px !important;                     /* COMPACT icon size */
    object-fit: contain !important;             /* Giữ tỷ lệ */
    filter: brightness(0) invert(1) drop-shadow(0 1px 2px rgba(0,0,0,0.3)) !important; /* White with shadow */
    margin-right: 6px !important;              /* MINIMAL spacing from text */
    flex-shrink: 0 !important;                 /* Prevent shrinking */
}

/* Camera title - Tiêu đề camera (rõ ràng hơn) */
.camera-title {
    color: white;                                /* Màu chữ trắng */
    margin: 0;                                   /* Không margin */
    font-size: 30px !important;                 /* INCREASED font size to 30px */
    font-weight: var(--font-weight-semibold);   /* Độ đậm font */
    white-space: nowrap;                        /* Không xuống dòng */
}

/* Camera status - Trạng thái camera */
.camera-status {
    display: flex;                               /* Flexbox */
    align-items: center;                         /* Căn giữa theo chiều dọc */
    gap: var(--spacing-xs);                      /* Khoảng cách giữa dot và text */
    color: #ffffff !important;                   /* Màu chữ trắng (force override) */
    font-size: 15px;                           /* INCREASED font size by 2px */
    flex-shrink: 0;                             /* Không co lại */
}

/* Camera status text - Text trạng thái camera */
.camera-status span {
    color: #ffffff !important;                   /* Đảm bảo text màu trắng */
}

/* Enhanced camera status text - MAXIMUM READABILITY */
#cardStatus, #faceStatus {
    font-size: 30px !important;                /* INCREASED to 20px for maximum readability */
    font-weight: 600 !important;               /* Semi-bold for better readability */
    color: white !important;                   /* Ensure white text on dark header */
    text-shadow: 0 1px 2px rgba(0,0,0,0.3) !important; /* Text shadow for contrast */
    line-height: 1.2 !important;               /* Optimal line height */
}

/* Status dot - Chấm trạng thái camera */
.status-dot {
    width: 10px;                                 /* Chiều rộng */
    height: 10px;                                /* Chiều cao */
    border-radius: 50%;                          /* Hình tròn */
    background: var(--color-success);            /* Màu xanh */
}

/* Status dot khi captured - Chấm trạng thái khi đã chụp */
.status-dot.status-captured {
    background: #43e97b !important;             /* Màu xanh khi captured */
    box-shadow: 0 0 10px rgba(67, 233, 123, 0.5) !important; /* Hiệu ứng glow */
    animation: pulse-captured 2s infinite !important; /* Hiệu ứng nhấp nháy */
}

/* Animation cho status captured */
@keyframes pulse-captured {
    0% {
        box-shadow: 0 0 10px rgba(67, 233, 123, 0.5);
        transform: scale(1);
    }
    50% {
        box-shadow: 0 0 20px rgba(67, 233, 123, 0.8);
        transform: scale(1.1);
    }
    100% {
        box-shadow: 0 0 10px rgba(67, 233, 123, 0.5);
        transform: scale(1);
    }
}

/* CAMERA FRAME & STREAM */

/* Camera frame - ABSOLUTE MAXIMUM SIZE */
.camera-frame {
    position: relative !important;              /* Để định vị overlay */
    width: 100% !important;                     /* ABSOLUTE MAXIMUM WIDTH 100% */
    height: 0 !important;                       /* Chiều cao 0 để dùng padding-bottom */
    padding-bottom: 85% !important;            /* INCREASED HEIGHT - Much taller cameras */
    overflow: hidden !important;                /* Ẩn nội dung tràn */
    flex: 1 !important;                         /* Chiếm không gian còn lại */
    background: linear-gradient(135deg, #000 0%, #1a1a1a 100%) !important; /* Gradient đen */
    margin: 0 !important;                       /* NO margin for maximum space */
    border-radius: 3px !important;              /* ULTRA MINIMAL border radius */
    border: 1px solid #667eea !important;       /* THINNER border */
    box-sizing: border-box !important;          /* Đảm bảo viền không ảnh hưởng kích thước */
    box-shadow: 0 2px 10px rgba(102, 126, 234, 0.1) !important; /* MINIMAL shadow */
}

/* Camera stream - ABSOLUTE MAXIMUM SIZE VIDEO */
.camera-stream {
    position: absolute !important;              /* Absolute positioning */
    top: 0 !important;                          /* Top 0 */
    left: 0 !important;                         /* Left 0 */
    width: 100% !important;                     /* ABSOLUTE MAXIMUM WIDTH */
    height: 100% !important;                    /* ABSOLUTE MAXIMUM HEIGHT */
    object-fit: cover !important;               /* Cắt để vừa khung */
    display: block !important;                  /* Block element */
    border-radius: 3px !important;              /* ULTRA MINIMAL border radius */
}

/* ========================================
   CAMERA OVERLAY GUIDES
   ======================================== */

/* Camera overlay guides - Hướng dẫn overlay trên camera (cân đối) */
.camera-overlay-guides {
    position: absolute;                          /* Định vị tuyệt đối */
    top: 0;                                      /* Từ trên cùng */
    left: 0;                                     /* Từ trái */
    right: 0;                                    /* Đến phải */
    bottom: 0;                                   /* Đến dưới */
    pointer-events: none;                        /* Không chặn click */
    z-index: 10;                                /* Đảm bảo hiển thị trên camera */
}

/* Guide corner - Góc hướng dẫn */
.guide-corner {
    position: absolute;                          /* Định vị tuyệt đối */
    width: 20px;                                 /* Chiều rộng */
    height: 20px;                                /* Chiều cao */
    border: 2px solid rgba(255, 255, 255, 0.8); /* Viền trắng trong suốt */
}

/* Guide corner positions - Vị trí các góc */
.guide-corner.tl {                              /* Top Left - Góc trên trái */
    top: 10px;
    left: 10px;
    border-right: none;                          /* Không viền phải */
    border-bottom: none;                         /* Không viền dưới */
}

.guide-corner.tr {                              /* Top Right - Góc trên phải */
    top: 10px;
    right: 10px;
    border-left: none;                           /* Không viền trái */
    border-bottom: none;                         /* Không viền dưới */
}

.guide-corner.bl {                              /* Bottom Left - Góc dưới trái */
    bottom: 10px;
    left: 10px;
    border-right: none;                          /* Không viền phải */
    border-top: none;                            /* Không viền trên */
}

.guide-corner.br {                              /* Bottom Right - Góc dưới phải */
    bottom: 10px;
    right: 10px;
    border-left: none;                           /* Không viền trái */
    border-top: none;                            /* Không viền trên */
}

/* Debug: MAXIMUM CAMERA SIZE - ULTRA TALL CAMERAS */
.camera-container:nth-child(1),
.camera-container:nth-child(2) {
    min-height: 450px !important;              /* MUCH TALLER cameras */
    max-height: 450px !important;              /* MUCH TALLER max height */
}

/*   OCR RESULTS OVERLAY - Đã được định nghĩa trong results-display.css */
/*   Xóa để tránh xung đột CSS */
/*   GENERATED IMAGES OVERLAY - Đè lên camera streams sau OCR */
.generated-images-overlay {
    position: absolute;
    inset: 0;
    z-index: 110; 
    background: var(--bg-card);
    border-radius: var(--radius-2xl);
    padding: var(--spacing-lg); /* Giảm padding để ảnh lớn hơn */
    border: 2px solid rgba(76, 175, 80, 0.3);
    box-shadow: 0 20px 60px rgba(76, 175, 80, 0.2);
    animation: fadeIn 0.6s ease-out;
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
}

.generated-images-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-md);
    border-bottom: 2px solid rgba(255,255,255,0.1);
}

.generated-images-title {
    font-size: 28px;
    font-weight: 700;
    color: var(--color-text-primary);
    margin: 0;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.generated-images-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
    height: 100%;
    min-height: 400px;
}

/* Single image layout - chỉ hiển thị 1 ảnh AI */
.generated-images-grid.single-image {
    grid-template-columns: 1fr;
    max-width: 800px; /* Tăng max-width để ảnh lớn hơn */
    margin: 0 auto;
    justify-content: center;
    align-items: center;
    height: auto; /* Để container fit với ảnh */
    min-height: auto; /* Bỏ min-height cố định */
}

.generated-image-container {
    position: relative !important;
    background: rgba(255,255,255,0.08) !important;
    border: 2px solid rgba(255,255,255,0.2) !important;
    border-radius: var(--radius-lg) !important;
    overflow: hidden !important;
    transition: all 0.3s ease !important;
    width: 100% !important;
    height: auto !important; /* Để container fit với ảnh */
    max-height: 80vh !important; /* Giới hạn chiều cao tối đa */
}

.generated-image-container:hover {
    border-color: rgba(76, 175, 80, 0.4);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(76, 175, 80, 0.3);
}

.generated-image {
    width: 100% !important;
    height: auto !important; /* Để ảnh giữ tỷ lệ khung hình */
    object-fit: contain !important; /* Thay đổi từ cover sang contain */
    display: block !important;
    max-height: 80vh !important; /* Giới hạn chiều cao tối đa */
}

.image-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0,0,0,0.7));
    padding: var(--spacing-md);
    color: white;
}

.image-label {
    font-size: 16px;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0,0,0,0.5);
}