
from typing import List, Optional
from datetime import datetime, timedelta
from database.models.checkin import Checkin
from database.connection import db
from .base import BaseCRUD

class CheckinCRUD(BaseCRUD):
    
    def __init__(self):
        super().__init__(Checkin)
    
    def create_checkin(self, customer_id: int, history_id: Optional[int] = None,
                      card_img: Optional[str] = None, face_img: Optional[str] = None,
                      ai_img: Optional[str] = None) -> Checkin:
        return self.create(
            customer_id=customer_id,
            history_id=history_id,
            checkin_time=datetime.now(),
            card_img=card_img,
            face_img=face_img,
            ai_img=ai_img
        )
    
    def get_by_customer(self, customer_id: int) -> List[Checkin]:
        return Checkin.query.filter_by(customer_id=customer_id).order_by(Checkin.checkin_time.desc()).all()
    
    def get_recent_checkins(self, limit: int = 10) -> List[Checkin]:
        return Checkin.query.order_by(Checkin.checkin_time.desc()).limit(limit).all()
    
    def get_checkins_by_date_range(self, start_date: datetime, end_date: datetime) -> List[Checkin]:
        return Checkin.query.filter(
            Checkin.checkin_time >= start_date,
            Checkin.checkin_time <= end_date
        ).order_by(Checkin.checkin_time.desc()).all()
    
    def get_today_checkins(self) -> List[Checkin]:
        today = datetime.now().date()
        start_of_day = datetime.combine(today, datetime.min.time())
        end_of_day = datetime.combine(today, datetime.max.time())
        return self.get_checkins_by_date_range(start_of_day, end_of_day)
    
    def get_with_customer_info(self, checkin_id: int) -> Checkin:
        from sqlalchemy.orm import joinedload
        return Checkin.query.options(joinedload(Checkin.customer)).get(checkin_id)
    
    def update_images(self, checkin_id: int, card_img: Optional[str] = None,
                     face_img: Optional[str] = None, ai_img: Optional[str] = None) -> Checkin:
        update_data = {}
        if card_img is not None:
            update_data['card_img'] = card_img
        if face_img is not None:
            update_data['face_img'] = face_img
        if ai_img is not None:
            update_data['ai_img'] = ai_img
        
        return self.update(checkin_id, **update_data)
    
    def delete_old_checkins(self, days_old: int = 30) -> int:
        cutoff_date = datetime.utcnow() - timedelta(days=days_old)
        old_checkins = Checkin.query.filter(Checkin.checkin_time < cutoff_date).all()
        count = len(old_checkins)
        
        for checkin in old_checkins:
            db.session.delete(checkin)
        
        db.session.commit()
        return count
