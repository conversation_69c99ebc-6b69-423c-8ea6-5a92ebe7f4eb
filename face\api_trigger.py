def trigger_auto_capture(session_id=None):
    try:
        import requests

        if not session_id:
            try:
                session_response = requests.get('http://localhost:3000/current_session_id', timeout=5)
                if session_response.status_code == 200:
                    session_data = session_response.json()
                    if session_data.get('status') == 'success':
                        session_id = session_data.get('session_id')
            except:
                pass

        payload = {}
        if session_id:
            payload['session_id'] = session_id

        if payload:
            response = requests.post('http://localhost:3000/auto_capture',
                                   json=payload, timeout=10)
        else:
            response = requests.post('http://localhost:3000/auto_capture', timeout=10)

        if response.status_code == 200:
            data = response.json()
            return data.get('status') == 'success'
        return False

    except:
        return False
