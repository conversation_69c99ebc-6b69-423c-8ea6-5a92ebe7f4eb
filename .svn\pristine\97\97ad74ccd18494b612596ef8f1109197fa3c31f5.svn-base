<!DOCTYPE html>
<html lang="vi">

<head>
    <!-- ========================================
         META TAGS & BASIC SETUP
         ======================================== -->
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Generator </title>
    <style>
        title {
            font-size: 35px !important;
        }
    </style>

    <!-- ========================================
         CACHE CONTROL
         ======================================== -->
    <!-- Prevent caching for dynamic content - Ngăn cache cho nội dung động -->
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">

    <!-- ========================================
         FONTS & TYPOGRAPHY
         ======================================== -->
    <!-- Vietnamese-supported Fonts - Font hỗ trợ tiếng Việt -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link
        href="https://fonts.googleapis.com/css2?family=Nunito:wght@300;400;500;600;700;800&family=Open+Sans:wght@300;400;500;600;700;800&display=swap"
        rel="stylesheet">

    <!-- Font Awesome Icons - Biểu tượng Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- ========================================
         EXTERNAL STYLESHEETS
         ======================================== -->
    <!-- ========================================
         MODULAR CSS FILES - Loaded in dependency order
         All Apple Design System styles integrated into modules
         ======================================== -->
    <!-- Core layout and navigation -->
    <link rel="stylesheet"
        href="{{ url_for('static', filename='css/layout-core.css') }}?t={{ range(100000, 999999) | random }}">

    <!-- Progressive disclosure and animations -->
    <link rel="stylesheet"
        href="{{ url_for('static', filename='css/progressive-disclosure.css') }}?t={{ range(100000, 999999) | random }}">

    <!-- Camera interface styles -->
    <link rel="stylesheet"
        href="{{ url_for('static', filename='css/camera-styles.css') }}?t={{ range(100000, 999999) | random }}">

    <!-- Controls and form styling -->
    <link rel="stylesheet"
        href="{{ url_for('static', filename='css/controls-styles.css') }}?t={{ range(100000, 999999) | random }}">

    <!-- Results display and card info -->
    <link rel="stylesheet"
        href="{{ url_for('static', filename='css/results-display.css') }}?t={{ range(100000, 999999) | random }}">

    <!-- Responsive design and media queries -->
    <link rel="stylesheet"
        href="{{ url_for('static', filename='css/responsive-design.css') }}?t={{ range(100000, 999999) | random }}">



</head>

<body>
    <!-- ========================================
         NAVIGATION BAR
         ======================================== -->
    <!-- Clean Navigation - Thanh điều hướng chính -->
    <nav class="apple-nav">
        <div class="apple-nav-content">
            <!-- Logo and Title - Logo và tiêu đề (căn giữa) -->
            <a href="/" class="apple-logo">
                <!-- Logo Image - Hình ảnh logo -->
                <img src="{{ url_for('static', filename='images/ssg-logo.png') }}" alt="SSG Logo" class="nav-logo-img">
                <!-- Title Text - Text tiêu đề -->
                <span class="nav-title">AI Generator</span>
            </a>

            <!-- Admin Badge - Thông báo Admin (chỉ hiển thị khi có query param admin=true) -->
            <div id="adminBadge" class="admin-badge" style="display: none;">
                <span class="admin-badge-icon">⚙️</span>
                <span class="admin-badge-text">ADMIN MODE</span>
            </div>
        </div>
    </nav>

    <!-- ========================================
         MAIN CONTENT CONTAINER
         ======================================== -->
    <!-- Single Page Container - Container chính chứa toàn bộ nội dung -->
    <div class="single-page-container">

        <!-- ========================================
             COMBINED CAMERA & AI GENERATION SECTION
             ======================================== -->
        <!-- Combined Section - Gộp chụp ảnh và tạo AI -->
        <div id="cameraSection" class="workflow-section">
            <div class="section-body">
                <div class="camera-grid">

                    <!-- ========================================
                         BUSINESS CARD CAMERA
                         ======================================== -->
                    <!-- Business Card Camera - Camera chụp business card -->
                    <div class="camera-container">
                        <!-- Camera Header - Header của camera -->
                        <div class="camera-header">
                            <!-- Camera Info - Thông tin camera -->
                            <div class="camera-info">
                                <!-- Business Card Icon - Icon business card (cải thiện) -->
                                <img src="{{ url_for('static', filename='images/business-card-icon.png') }}"
                                    alt="Business Card" class="camera-icon-img">
                                <h3 class="camera-title">Business Card</h3> <!-- Tiêu đề có thể thay đổi -->
                            </div>
                            <!-- Camera Status - Trạng thái camera -->
                            <div class="camera-status">
                                <div id="status0" class="status-dot"></div>
                                <span id="cardStatus">📄</span>
                            </div>
                        </div>
                        <!-- Camera Frame - Khung chứa video -->
                        <div class="camera-frame">
                            <!-- WebRTC Video Stream - Video stream WebRTC (ẩn mặc định) -->
                            <video id="cam0-webrtc" autoplay muted playsinline class="camera-stream"
                                style="display: none;"></video>
                            <!-- Camera Image Stream - Image stream từ server -->
                            <img id="cam0" src="/card_detection_feed" alt="Business Card Camera" class="camera-stream">
                            <!-- Overlay Guides - Hướng dẫn overlay -->
                            <div class="camera-overlay-guides">
                                <div class="guide-corner tl"></div> <!-- Top Left -->
                                <div class="guide-corner tr"></div> <!-- Top Right -->
                                <div class="guide-corner bl"></div> <!-- Bottom Left -->
                                <div class="guide-corner br"></div> <!-- Bottom Right -->
                            </div>
                        </div>
                    </div>

                    <!-- ========================================
                         FACE CAMERA
                         ======================================== -->
                    <!-- Face Camera - Camera chụp khuôn mặt -->
                    <div class="camera-container">
                        <!-- Camera Header - Header của camera -->
                        <div class="camera-header">
                            <!-- Camera Info - Thông tin camera -->
                            <div class="camera-info">
                                <!-- Face ID Icon - Icon face ID (cải thiện) -->
                                <img src="{{ url_for('static', filename='images/face-id-icon.png') }}" alt="Face ID"
                                    class="camera-icon-img">
                                <h3 class="camera-title">Face Photo</h3> <!-- Tiêu đề có thể thay đổi -->
                            </div>
                            <!-- Camera Status - Trạng thái camera -->
                            <div class="camera-status">
                                <div id="status1" class="status-dot"></div>
                                <span id="faceStatus">😊</span>
                            </div>
                        </div>
                        <!-- Camera Frame - Khung chứa video -->
                        <div class="camera-frame">
                            <!-- WebRTC Video Stream - Video stream WebRTC (ẩn mặc định) -->
                            <!-- <video id="cam1-webrtc" autoplay muted playsinline class="camera-stream" style="display: none;"></video> -->
                            <!-- Camera Image Stream - Image stream từ server -->
                            <img id="cam1" src="/face_detection_feed" alt="Face Camera" class="camera-stream">
                            <!-- Overlay Guides - Hướng dẫn overlay -->
                            <div class="camera-overlay-guides">
                                <div class="guide-corner tl"></div> <!-- Top Left -->
                                <div class="guide-corner tr"></div> <!-- Top Right -->
                                <div class="guide-corner bl"></div> <!-- Bottom Left -->
                                <div class="guide-corner br"></div> <!-- Bottom Right -->
                            </div>
                        </div>
                    </div>

                </div>

                <!-- Admin Controls Section - Hiển thị trong admin mode -->
                <div class="admin-controls-section">
                    <div class="admin-controls-header">
                        <h3 class="admin-controls-title">🔧 Admin Controls</h3>
                    </div>
                    <div class="admin-controls-buttons">
                        <button class="admin-btn capture-card-btn" id="adminCaptureCardBtn">
                            📄 Chụp card
                        </button>
                        <button class="admin-btn capture-face-btn" id="adminCaptureFaceBtn">
                            👤 Chụp face
                        </button>
                        <button class="admin-btn reset-btn" id="adminResetBtn">
                            🔄 Reset
                        </button>
                    </div>
                </div>




                <!-- OCR Results Section - Business Card Design -->
                {% include 'ocr_results.html' %}

                <!-- Generated Images Section - Đè lên camera streams sau OCR -->
                <div id="generatedImagesOverlay" class="generated-images-overlay" style="display: none;">
                    <!-- <div class="generated-images-header">
                            <h2 class="generated-images-title">🎨 Ảnh AI đã tạo</h2>
                        </div> -->
                    <div class="generated-images-grid single-image">
                        <div class="generated-image-container">
                            <img id="generatedImage1" class="generated-image" src="" alt="Generated AI Image">
                            <div class="image-overlay">
                                <span class="image-label">Ảnh AI</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- ========================================
             ADMIN CONTROLS SECTION (chỉ hiển thị ở admin mode)
             ======================================== -->
    <div id="adminControlsSection" class="admin-controls-section">
        <div class="admin-controls-container">
            <div class="admin-controls-header">
                <h2 class="admin-controls-title">🔧 Admin Controls</h2>
                <p class="admin-controls-subtitle">Điều khiển thủ công các chức năng hệ thống</p>
            </div>

            <div class="admin-controls-grid">
                <button id="adminCaptureCard" class="admin-btn admin-btn-card">
                    <span class="admin-btn-icon">📄</span>
                    <span class="admin-btn-text">Chụp Card</span>
                </button>

                <button id="adminCaptureFace" class="admin-btn admin-btn-face">
                    <span class="admin-btn-icon">👤</span>
                    <span class="admin-btn-text">Chụp Face</span>
                </button>

                <button id="adminGenerateAI" class="admin-btn admin-btn-generate">
                    <span class="admin-btn-icon">🎨</span>
                    <span class="admin-btn-text">Gen Ảnh</span>
                </button>

                <button id="adminReset" class="admin-btn admin-btn-reset">
                    <span class="admin-btn-icon">🔄</span>
                    <span class="admin-btn-text">Reset</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Style Description - Mô tả style đã chọn (ẩn) -->
    <div id="promptDescription" style="display: none;">
        Chọn style để xem mô tả <!-- Text mặc định -->
    </div>

    <!-- Progress Bar - Thanh tiến trình (ẩn mặc định) -->
    <div id="progressBar" style="display: none;">
        <!-- Progress Bar Container - Container thanh tiến trình -->
        <div class="apple-progress-bar"
            style="height: 8px; background: rgba(255,255,255,0.1); border-radius: var(--radius-sm); margin-bottom: var(--spacing-sm);">
            <!-- Progress Fill - Phần fill của thanh tiến trình -->
            <div class="apple-progress-fill" id="progressFill"
                style="background: var(--primary-gradient); height: 100%; width: 0%; transition: width 0.3s ease;">
            </div>
        </div>
        <!-- Progress Text - Text mô tả tiến trình -->
        <p style="font-size: 14px; color: var(--color-text-secondary); margin: 0; text-align: center;">
            Đang xử lý... <!-- Text có thể thay đổi -->
        </p>
    </div>

    <!-- Retake button moved to be adjacent to main button above -->
    </div>
    </div>
    </div>
    <div style="text-align: center; margin-bottom: var(--spacing-xl);">
        <div id="statusMessage" style="font-size: 16px; color: var(--color-text-secondary);"></div>
        <!-- Sẽ được cập nhật bởi JS -->
    </div>

    <!-- Auto-reset loading UI -->
    <div id="autoResetLoading"
        style="display: none; position: fixed; bottom: 20px; left: 20px; z-index: 1000; background: rgba(0, 0, 0, 0.85); padding: 16px 20px; border-radius: 12px; border: 2px solid rgba(67, 233, 123, 0.6); box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3); text-align: left; min-width: 280px; max-width: 350px; backdrop-filter: blur(10px);">
        <div style="display: flex; align-items: center; margin-bottom: 16px;">
            <div style="font-size: 24px; margin-right: 12px;">🔄</div>
            <p style="margin: 0; color: #ffffff; font-size: 20px; font-weight: 700;">AUTO reset</p>
        </div>
        <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 16px;">
            <span id="countdownSeconds" style="color: #43e97b; font-weight: bold; font-size: 24px; margin-right: 8px;">10</span>
            <span style="color: #e0e0e0; font-size: 18px; font-weight: 500;">seconds</span>
        </div>
        <div
            style="background: rgba(255,255,255,0.15); border-radius: 10px; height: 16px; overflow: hidden; margin-bottom: 12px; position: relative;">
            <div id="loadingBar"
                style="background: linear-gradient(90deg, #43e97b 0%, #38f9d7 100%); height: 100%; width: 0%; transition: width 1s ease; border-radius: 10px; box-shadow: 0 0 12px rgba(67, 233, 123, 0.6);">
            </div>
        </div>
        <p style="margin: 0; font-size: 12px; color: #b0b0b0;">Preparing for next capture...</p>
    </div>
    </div>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    <script src="{{ url_for('static', filename='js/webrtc-camera.js') }}"></script>
    <script src="{{ url_for('static', filename='js/face-camera-monitor.js') }}"></script>
    <script
        src="{{ url_for('static', filename='js/card-camera-monitor.js') }}?t={{ range(100000, 999999) | random }}"></script>
    <script src="{{ url_for('static', filename='js/utils.js') }}?t={{ range(100000, 999999) | random }}"></script>
    <script
        src="{{ url_for('static', filename='js/workflow-manager.js') }}?t={{ range(100000, 999999) | random }}"></script>
    <script
        src="{{ url_for('static', filename='js/ui-animations.js') }}?t={{ range(100000, 999999) | random }}"></script>
    <script
        src="{{ url_for('static', filename='js/ui-components.js') }}?t={{ range(100000, 999999) | random }}"></script>
    <script
        src="{{ url_for('static', filename='js/session-manager.js') }}?t={{ range(100000, 999999) | random }}"></script>
    <script src="{{ url_for('static', filename='js/api-client.js') }}?t={{ range(100000, 999999) | random }}"></script>
    <script>
        // Check admin mode from query parameter
        function checkAdminMode() {
            const urlParams = new URLSearchParams(window.location.search);
            const isAdmin = urlParams.get('admin') === 'true';

            const adminBadge = document.getElementById('adminBadge');
            const adminControlsSection = document.getElementById('adminControlsSection');

            if (adminBadge && isAdmin) {
                adminBadge.style.display = 'flex';
                console.log('🔐 Admin mode activated');

                // Show admin controls section
                if (adminControlsSection) {
                    adminControlsSection.style.display = 'block';
                }

                // Add admin-specific functionality
                document.body.classList.add('admin-mode');
                initAdminControls();
            } else {
                if (adminBadge) {
                    adminBadge.style.display = 'none';
                }
                if (adminControlsSection) {
                    adminControlsSection.style.display = 'none';
                }
                document.body.classList.remove('admin-mode');
            }
        }

        // Initialize admin controls
        function initAdminControls() {
            console.log('🔧 Initializing admin controls...');
            console.log('🔍 Admin mode active, looking for buttons...');

            // Admin Capture Card button (old duplicate) - REMOVED
            // const adminCaptureCard = document.getElementById('adminCaptureCard');
            // if (adminCaptureCard) {
            //     adminCaptureCard.onclick = function() {
            //         console.log('📄 Admin: Capture Card clicked');
            //         // Trigger card capture
            //         if (typeof captureCard === 'function') {
            //             captureCard();
            //         } else {
            //             // Fallback to manual capture
            //             fetch('/capture_card', {
            //                 method: 'POST',
            //                 headers: {'Content-Type': 'application/json'}
            //             })
            //             .then(response => response.json())
            //             .then(data => {
            //                 console.log('✅ Card captured:', data);
            //                 showMessage('Card đã được chụp thành công', 'success');
            //             })
            //             .catch(error => {
            //                 console.error('❌ Card capture error:', error);
            //                 showMessage('Lỗi khi chụp card', 'error');
            //             });
            //         }
            //     };
            // }

            // Admin Capture Card button (new in camera section)
            const adminCaptureCardBtn = document.getElementById('adminCaptureCardBtn');
            console.log('🔍 Looking for adminCaptureCardBtn:', adminCaptureCardBtn);
            if (adminCaptureCardBtn) {
                console.log('✅ Found adminCaptureCardBtn, setting up click handler');
                adminCaptureCardBtn.onclick = function () {
                    console.log('📄 Admin: Capture Card Button clicked (camera section)');
                    console.log('🔍 captureImageSimultaneous function exists:', typeof captureImageSimultaneous);
                    // Capture card camera (camera 0)
                    if (typeof captureImageSimultaneous === 'function') {
                        captureImageSimultaneous(0,
                            function () {
                                console.log('✅ Card camera captured successfully');
                                showMessage('✅ Card đã được chụp thành công', 'success');
                            },
                            function (error) {
                                console.error('❌ Card capture error:', error);
                                showMessage('❌ Lỗi khi chụp card', 'error');
                            }
                        );
                    } else {
                        console.error('❌ captureImageSimultaneous function not found');
                    }
                };
            } else {
                console.error('❌ adminCaptureCardBtn not found in DOM');
            }

            // Admin Capture Face button (new in camera section)
            const adminCaptureFaceBtn = document.getElementById('adminCaptureFaceBtn');
            console.log('🔍 Looking for adminCaptureFaceBtn:', adminCaptureFaceBtn);
            if (adminCaptureFaceBtn) {
                console.log('✅ Found adminCaptureFaceBtn, setting up click handler');
                adminCaptureFaceBtn.onclick = function () {
                    console.log('👤 Admin: Capture Face Button clicked (camera section)');
                    console.log('🔍 captureImageSimultaneous function exists:', typeof captureImageSimultaneous);
                    // Capture face camera (camera 1)
                    if (typeof captureImageSimultaneous === 'function') {
                        captureImageSimultaneous(1,
                            function () {
                                console.log('✅ Face camera captured successfully');
                                showMessage('✅ Face đã được chụp thành công', 'success');
                            },
                            function (error) {
                                console.error('❌ Face capture error:', error);
                                showMessage('❌ Lỗi khi chụp face', 'error');
                            }
                        );
                    } else {
                        console.error('❌ captureImageSimultaneous function not found');
                    }
                };
            } else {
                console.error('❌ adminCaptureFaceBtn not found in DOM');
            }

            // Admin Reset button (new in camera section)
            const adminResetBtn = document.getElementById('adminResetBtn');
            console.log('🔍 Looking for adminResetBtn:', adminResetBtn);
            if (adminResetBtn) {
                console.log('✅ Found adminResetBtn, setting up click handler');
                adminResetBtn.onclick = function () {
                    console.log('🔄 Admin: Reset Button clicked (camera section)');
                    if (confirm('Bạn có chắc muốn reset toàn bộ session và reload trang?')) {
                        console.log('🔄 Reloading page...');
                        // Reload page with admin parameter
                        const url = new URL(window.location.href);
                        url.searchParams.set('admin', 'true');
                        url.searchParams.set('t', Date.now());
                        window.location.href = url.toString();
                    }
                };
            } else {
                console.error('❌ adminResetBtn not found in DOM');
            }

            // Admin Capture Face button (old duplicate) - REMOVED
            // const adminCaptureFace = document.getElementById('adminCaptureFace');
            // if (adminCaptureFace) {
            //     adminCaptureFace.onclick = function() {
            //         console.log('👤 Admin: Capture Face clicked');
            //         // Trigger face capture
            //         if (typeof captureFace === 'function') {
            //             captureFace();
            //         } else {
            //             // Fallback to manual capture
            //             fetch('/capture_face', {
            //                 method: 'POST',
            //                 headers: {'Content-Type': 'application/json'}
            //             })
            //             .then(response => response.json())
            //             .then(data => {
            //                 console.log('✅ Face captured:', data);
            //                 showMessage('Face đã được chụp thành công', 'success');
            //             })
            //             .catch(error => {
            //                 console.error('❌ Face capture error:', error);
            //                 showMessage('Lỗi khi chụp face', 'error');
            //             });
            //         }
            //     };
            // }

            // Admin Generate AI button
            const adminGenerateAI = document.getElementById('adminGenerateAI');
            if (adminGenerateAI) {
                adminGenerateAI.onclick = function () {
                    console.log('🎨 Admin: Generate AI clicked');
                    adminGenerateAI.disabled = true;
                    adminGenerateAI.innerHTML = '<span class="admin-btn-icon">⏳</span><span class="admin-btn-text">Đang gen...</span>';

                    // Trigger AI generation
                    fetch('/api/retry_ai_generation', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' }
                    })
                        .then(response => response.json())
                        .then(data => {
                            console.log('✅ AI generated:', data);
                            showMessage('Ảnh AI đã được tạo thành công', 'success');
                            loadAndDisplayResults(); // Refresh results
                        })
                        .catch(error => {
                            console.error('❌ AI generation error:', error);
                            showMessage('Lỗi khi tạo ảnh AI', 'error');
                        })
                        .finally(() => {
                            adminGenerateAI.disabled = false;
                            adminGenerateAI.innerHTML = '<span class="admin-btn-icon">🎨</span><span class="admin-btn-text">Gen Ảnh</span>';
                        });
                };
            }

            // Admin Reset button
            const adminReset = document.getElementById('adminReset');
            if (adminReset) {
                adminReset.onclick = function () {
                    console.log('🔄 Admin: Reset clicked');
                    if (confirm('Bạn có chắc muốn reset toàn bộ session?')) {
                        adminReset.disabled = true;
                        adminReset.innerHTML = '<span class="admin-btn-icon">⏳</span><span class="admin-btn-text">Đang reset...</span>';

                        // Clear session
                        fetch('/api/clear_session', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' }
                        })
                            .then(response => response.json())
                            .then(data => {
                                console.log('✅ Session cleared:', data);
                                showMessage('Session đã được reset', 'success');
                                // Reload page with admin parameter
                                const url = new URL(window.location.href);
                                url.searchParams.set('admin', 'true');
                                url.searchParams.set('t', Date.now());
                                window.location.href = url.toString();
                            })
                            .catch(error => {
                                console.error('❌ Reset error:', error);
                                showMessage('Lỗi khi reset session', 'error');
                            })
                            .finally(() => {
                                adminReset.disabled = false;
                                adminReset.innerHTML = '<span class="admin-btn-icon">🔄</span><span class="admin-btn-text">Reset</span>';
                            });
                    }
                };
            }

            console.log('✅ Admin controls initialized');
        }

        function initializeSinglePage() {
            console.log('🚀 Initializing single page layout...');

            // Check admin mode first
            checkAdminMode();

            resetAllSections();
            loadAndDisplayResults();

            setTimeout(() => {
                trackWorkflowStatus();
            }, 1000);

            const combinedBtn = document.getElementById('combinedActionBtn');
            if (combinedBtn) {
                combinedBtn.onclick = combinedCaptureAndGenerate;
                console.log('✅ Combined action button initialized');
            }

            setInterval(() => {
                loadAndDisplayResults();
            }, 10000);

            console.log('✅ Single page layout initialized with progressive disclosure');
        }

        document.addEventListener('DOMContentLoaded', function () {
            initializeSinglePage();
            document.body.classList.add('no-scroll');
            document.body.classList.remove('allow-scroll');
        });
    </script>
</body>

</html>

<!-- ========================================
     END OF HTML DOCUMENT

     HƯỚNG DẪN CHỈNH SỬA GIAO DIỆN:

     1. THAY ĐỔI MÀU SẮC:
        - Tìm các biến CSS như var(--color-primary), var(--bg-card)
        - Thay đổi trong file apple-design.css hoặc thêm style inline

     2. THAY ĐỔI ICON:
        - Tìm các emoji như 📸, 🎨, 👤, 📄
        - Thay thế bằng emoji khác hoặc icon font

     3. THAY ĐỔI TEXT:
        - Tìm text trong các comment "có thể thay đổi"
        - Sửa trực tiếp trong HTML

     4. THAY ĐỔI LAYOUT:
        - Tìm các class CSS như .camera-grid, .controls-grid
        - Sửa grid-template-columns để thay đổi số cột

     5. THAY ĐỔI KÍCH THƯỚC:
        - Tìm các thuộc tính như font-size, padding, margin
        - Sử dụng các biến spacing như var(--spacing-lg)

     6. THÊM/XÓA SECTION:
        - Copy/paste cấu trúc .workflow-section
        - Cập nhật JavaScript tương ứng

     ======================================== -->