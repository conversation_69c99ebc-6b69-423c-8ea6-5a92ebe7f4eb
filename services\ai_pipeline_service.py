#!/usr/bin/env python3
"""
Three-Stage AI Pipeline Service for Business Card Processing and Image Generation

Stage 1: OCR Processing with Gemini 2.5 Flash
Stage 1.5: TTS Processing with Google TTS
Stage 2: AI Image Generation with Gemini 2.0 Flash Preview
"""

import os
import json
import time
import threading
import queue
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Tuple

# Import OCR service (Stage 1)
from gemini_ocr_service import GeminiOCRService

from services.tts_service import init_tts_service  # TTS service enabled

# Import AI image generator (Stage 2)
from ai_generator import AIImageGenerator

# Import configuration
from ai_config import get_gemini_config

class AIProcessingPipeline:
    """
    Three-Stage AI pipeline for business card processing and image generation

    Stage 1: OCR Processing (Gemini 2.5 Flash)
    - Extract text from business card images with maximum accuracy
    - Apply image preprocessing for better OCR results
    - Use deterministic API settings for consistent results

    Stage 1.5: TTS Processing with Google TTS
    - Convert OCR results to speech
    - Auto-play audio for extracted information

    Stage 2: AI Image Generation (Gemini 2.0 Flash Preview)
    - Generate AI images using multimodal capabilities
    - Combine prompt template, OCR data, and face image
    - Create multiple variants with proper metadata
    """

    def __init__(self):
        """Initialize the Three-Stage AI pipeline"""
        print("🔧 Initializing Three-Stage AI Pipeline...")

        # Initialize Stage 1: OCR Service (Gemini 2.5 Flash)
        print("📝 Stage 1: Initializing OCR Service (Gemini 2.5 Flash)...")
        try:
            self.ocr_service = GeminiOCRService()
            print("✅ Stage 1 OCR Service initialized successfully")
        except Exception as e:
            print(f"❌ Stage 1 OCR Service initialization failed: {e}")
            self.ocr_service = None

        # Initialize Stage 1.5: TTS Service (Google TTS)
        print("📢 Stage 1.5: Initializing TTS Service (Google TTS)...")
        try:
            self.tts_service = init_tts_service("outputs/audio")
            print("✅ Stage 1.5 TTS Service initialized successfully")
        except Exception as e:
            print(f"❌ Stage 1.5 TTS Service initialization failed: {e}")
            self.tts_service = None

        # Initialize Stage 2: AI Image Generator (Gemini 2.0 Flash Preview)
        print("🎨 Stage 2: Initializing AI Image Generator (Gemini 2.0 Flash Preview)...")

        try:
            self.ai_generator = AIImageGenerator()
            print("✅ Stage 2 AI Generator initialized successfully")
        except Exception as e:
            print(f"❌ Stage 2 AI Generator initialization failed: {e}")
            self.ai_generator = None

        # Pipeline configuration
        try:
            self.config = get_gemini_config()
            print("✅ Pipeline configuration loaded")
        except Exception as e:
            print(f"❌ Pipeline configuration failed: {e}")
            self.config = {'model': 'gemini-2.0-flash-preview-image-generation'}

        # Pipeline state
        self.current_session = None
        self.stage1_result = None
        self.stage2_result = None

        # Validation
        if self.ocr_service and self.ai_generator:
            print("✅ Three-Stage AI Pipeline initialized successfully")
            print(f"   Stage 1: OCR with Gemini 2.5 Flash")
            print(f"   Stage 1.5: TTS with Google TTS")
            print(f"   Stage 2: Image Generation with Gemini 2.0 Flash Preview")
        else:
            print("⚠️ Three-Stage AI Pipeline initialized with errors")
            print(f"   Stage 1 OCR: {'✅' if self.ocr_service else '❌'}")
            print(f"   Stage 2 Generator: {'✅' if self.ai_generator else '❌'}")

    def reset_state(self):
        """Reset pipeline state cho workflow mới"""
        print("🔄 Resetting AI Pipeline state for new workflow...")
        self.current_session = None
        self.stage1_result = None
        self.stage2_result = None

        # Reset individual services
        if self.ocr_service and hasattr(self.ocr_service, 'reset_state'):
            self.ocr_service.reset_state()
        if self.ai_generator and hasattr(self.ai_generator, 'reset_state'):
            self.ai_generator.reset_state()

        print("✅ AI Pipeline state reset completed")

    def process_business_card(self, card_image_path: str, face_image_path: str,
                            prompt_template: str = 'cartoon', session_id: str = None) -> Dict:
        """
        Execute the complete three-stage AI pipeline

        Args:
            card_image_path: Path to business card image
            face_image_path: Path to face reference image
            prompt_template: Style template (cartoon/luxury/artistic)
            session_id: Optional session identifier

        Returns:
            Dict containing results from all stages
        """

        pipeline_start_time = time.time()

        # Reset timestamp cho session mới - Reset timestamp for new session
        from utils.path_manager import reset_session_timestamp
        reset_session_timestamp()
        print(f"🔄 Reset session timestamp for new pipeline run")

        print(f"\n🚀 Starting Three-Stage AI Pipeline")
        print(f"   Session ID: {session_id or 'auto-generated'}")
        print(f"   Card Image: {card_image_path}")
        print(f"   Face Image: {face_image_path}")
        print(f"   Style Template: {prompt_template}")
        print("=" * 60)

        # Initialize session
        if not session_id:
            session_id = f"pipeline_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        self.current_session = {
            'session_id': session_id,
            'start_time': pipeline_start_time,
            'card_image_path': card_image_path,
            'face_image_path': face_image_path,
            'prompt_template': prompt_template,
            'stage1_result': None,
            'stage1_5_result': None,
            'stage2_result': None,
            'status': 'processing'
        }

        try:
            # Stage 1: OCR Processing
            stage1_result = self._execute_stage1_ocr(card_image_path)
            if not stage1_result['success']:
                return self._create_error_result("Stage 1 OCR failed", stage1_result['error'])

            # Save OCR to session immediately so frontend can show CardInfo early
            try:
                from controllers.camera_controller import camera_controller  
                camera_controller.session_model.save_ocr_only(stage1_result['data'])
            except Exception as e:
                print(f"[EARLY SAVE] Failed to save OCR to session: {e}")

            # Xử lý Stage 1.5 và 2 song song bằng thread
            stage1_5_result, stage2_result = self._execute_parallel_stages(
                stage1_result['data'], face_image_path, prompt_template
            )

            if not stage2_result['success']:
                return self._create_error_result("Stage 2 Generation failed", stage2_result['error'])

            # Auto-play TTS after Stage 2
            self._trigger_autoplay_tts(stage1_5_result)

            # Pipeline completed successfully
            pipeline_end_time = time.time()
            total_time = pipeline_end_time - pipeline_start_time

            result = {
                'success': True,
                'session_id': session_id,
                'total_processing_time': f"{total_time:.2f}s",
                'stage1_ocr': stage1_result,
                'stage1_5_tts': stage1_5_result,
                'stage2_generation': stage2_result,
                'pipeline_summary': {
                    'card_info_extracted': stage1_result['data'],
                    'images_generated': len(stage2_result['data']['generated_images']),
                    'processing_stages': ['OCR (Gemini 2.5)', 'TTS Processing', 'Generation (Gemini 2.0)']
                }
            }

            self.current_session['status'] = 'completed'
            self.current_session['result'] = result

            print(f"\n✅ Three-Stage AI Pipeline Completed Successfully!")
            print(f"   Total Time: {total_time:.2f}s")
            print(f"   Stage 1 Time: {stage1_result['processing_time']}")
            print(f"   Stage 1.5 TTS: {stage1_5_result.get('status', 'completed')}")
            print(f"   Stage 2 Time: {stage2_result['processing_time']}")
            print(f"   Images Generated: {len(stage2_result['data']['generated_images'])}")

            return result

        except Exception as e:
            print(f"❌ Pipeline Error: {e}")
            return self._create_error_result("Pipeline execution failed", str(e))

    def _execute_parallel_stages(self, card_info: Dict, face_image_path: str, prompt_template: str) -> Tuple[Dict, Dict]:
        """Xử lý Stage 1.5 và Stage 2 song song bằng thread và queue"""
        print(f"\n🔄 Executing Stage 1.5 and 2 in parallel")

        # Tạo queue để nhận kết quả
        result_queue = queue.Queue()

        # Thread cho Stage 1.5 TTS
        def tts_worker():
            result = self._execute_stage1_5_tts(card_info)
            
            try:
                if result.get('success') and result.get('audio_path'):
                    from controllers.camera_controller import camera_controller  
                    camera_controller.session_model.current_session['tts_audio_path'] = result['audio_path']
            except Exception as e:
                print(f"[EARLY SAVE] Failed to save TTS audio to session: {e}")
            result_queue.put(('tts', result))

        # Thread cho Stage 2 Generation
        def generation_worker():
            result = self._execute_stage2_generation(face_image_path, card_info, prompt_template)
            result_queue.put(('generation', result))

        # Khởi tạo và chạy threads
        tts_thread = threading.Thread(target=tts_worker)
        generation_thread = threading.Thread(target=generation_worker)

        tts_thread.start()
        generation_thread.start()

        # Chờ kết quả từ queue
        stage1_5_result = None
        stage2_result = None

        for _ in range(2):  # Chờ 2 kết quả
            stage_type, result = result_queue.get()
            if stage_type == 'tts':
                stage1_5_result = result
            elif stage_type == 'generation':
                stage2_result = result

        # Chờ threads hoàn thành
        tts_thread.join()
        generation_thread.join()

        print(f"✅ Parallel execution completed")
        return stage1_5_result, stage2_result

    def _execute_stage1_5_tts(self, card_info: Dict) -> Dict:
        """Thực hiện Stage 1.5: Xử lý TTS từ kết quả OCR"""
        print(f"\n🔊 Stage 1.5: TTS Processing")

        # Kiểm tra TTS service có sẵn không
        if not self.tts_service:
            print("⚠️ TTS service not available - skipping Stage 1.5")
            return {'success': True, 'status': 'skipped', 'reason': 'TTS service not available'}

        try:
            # Tạo text từ thông tin business card và detect ngôn ngữ 1 lần
            name = card_info.get('name', '')
            detected_lang = self.tts_service.detect_language(name)
            print(f"🌐 Detected language from name: {detected_lang}")

            tts_text = self._create_tts_text_from_card_info(card_info, detected_lang)
            print(f"📝 TTS text created: {tts_text}")

            if not tts_text:
                print("⚠️ No TTS text generated - skipping")
                return {'success': True, 'status': 'skipped', 'reason': 'No text to convert'}

            # Tạo file MP3 với ngôn ngữ đã detect từ name
            audio_path = self.tts_service.text_to_speech(tts_text, lang=detected_lang)

            if audio_path:
                print(f"✅ TTS audio created: {audio_path}")
                return {
                    'success': True,
                    'status': 'completed',
                    'audio_path': audio_path,
                    'tts_text': tts_text,
                    'language': detected_lang
                }
            else:
                print("❌ TTS audio creation failed")
                return {'success': False, 'status': 'failed', 'error': 'Audio creation failed'}

        except Exception as e:
            print(f"❌ TTS processing error: {e}")
            return {'success': False, 'status': 'failed', 'error': str(e)}

    def _create_tts_text_from_card_info(self, card_info: Dict, lang: str) -> str:
        """Tạo lời chào từ template và thông tin business card"""
        if not card_info or not card_info.get('name'):
            return ""

        # Sử dụng template với ngôn ngữ đã detect
        if self.tts_service:
            # Đọc template theo ngôn ngữ đã được detect
            template = self.tts_service.load_template("greeting", lang)
            if template:
                # Thay thế placeholder bằng thông tin thực
                return self.tts_service.replace_placeholders(template, card_info)

        # Fallback nếu không có TTS service hoặc template
        return f"Hello, {card_info['name']}"

    def _trigger_autoplay_tts(self, stage1_5_result: Dict):
        """Kích hoạt auto-play TTS sau khi hoàn thành Stage 2"""
        if stage1_5_result.get('success') and stage1_5_result.get('audio_path'):
            print(f"🔊 Auto-play TTS: {stage1_5_result['audio_path']}")
            print(f"📁 Audio saved to: outputs/audio/ folder")
            # Auto-play sẽ được xử lý ở frontend


    def _execute_stage1_ocr(self, card_image_path: str) -> Dict:
        """
        Execute Stage 1: OCR Processing with Gemini 2.5 Flash

        Features:
        - Image preprocessing (resize, enhance, unsharp mask)
        - Deterministic API settings (temperature=0.0, topK=1, topP=0.1)
        - Enhanced OCR prompts for maximum accuracy
        - Structured JSON output parsing
        """

        print(f"\n📝 STAGE 1: OCR Processing (Gemini 2.5 Flash)")
        print(f"   Input: {Path(card_image_path).name}")

        stage1_start_time = time.time()

        try:
            # Validate input
            if not Path(card_image_path).exists():
                raise FileNotFoundError(f"Card image not found: {card_image_path}")

            # Execute OCR with enhanced preprocessing and prompts
            print("   🔍 Extracting text with maximum precision...")
            card_info = self.ocr_service.extract_text_from_card(card_image_path)

            if not card_info:
                raise ValueError("OCR returned no data")

            # Validate extracted data
            required_fields = ['name', 'title', 'company', 'email', 'phone', 'website', 'address', 'other_info']
            validated_info = {}

            for field in required_fields:
                validated_info[field] = card_info.get(field, '')

            # AUTO-RESET: Kiểm tra nếu có từ 2 field trở lên là None/empty
            empty_fields = [field for field, value in validated_info.items() if not value or value.strip() == '']
            empty_count = len(empty_fields)
            
            if empty_count >= 2:
                print(f"⚠️ OCR Quality Check Failed: {empty_count}/8 fields are empty")
                print(f"   Empty fields: {empty_fields}")
                print("🔄 Auto-resetting session due to poor OCR quality...")
                
                # Reset session
                try:
                    from controllers.camera_controller import camera_controller
                    camera_controller.session_model.clear_session()
                    print("✅ Session reset completed")
                except Exception as e:
                    print(f"❌ Error resetting session: {e}")
                
                # Trả về error đặc biệt để frontend có thể reset UI
                error_msg = f"OCR quality too low: {empty_count}/8 fields empty. Session auto-reset."
                error = ValueError(error_msg)
                error.auto_reset_ui = True  # Flag để frontend biết cần reset UI
                error.empty_fields = empty_fields
                error.empty_count = empty_count
                raise error

            stage1_end_time = time.time()
            processing_time = f"{stage1_end_time - stage1_start_time:.2f}s"

            result = {
                'success': True,
                'stage': 'OCR Processing',
                'model': 'Gemini 2.5 Flash',
                'processing_time': processing_time,
                'data': validated_info,
                'metadata': {
                    'image_path': card_image_path,
                    'preprocessing_applied': True,
                    'api_settings': 'Deterministic (temp=0.0, topK=1, topP=0.1)',
                    'fields_extracted': len([v for v in validated_info.values() if v]),
                    'empty_fields_count': empty_count,
                    'quality_check': 'PASSED' if empty_count < 2 else 'FAILED'
                }
            }

            self.stage1_result = result

            print(f"   ✅ Stage 1 Completed: {processing_time}")
            print(f"   📊 Fields Extracted: {result['metadata']['fields_extracted']}/7")
            print(f"   👤 Name: {validated_info.get('name', 'N/A')}")
            print(f"   🏢 Company: {validated_info.get('company', 'N/A')}")
            print(f"   📧 Email: {validated_info.get('email', 'N/A')}")

            return result

        except Exception as e:
            stage1_end_time = time.time()
            processing_time = f"{stage1_end_time - stage1_start_time:.2f}s"

            print(f"   ❌ Stage 1 Failed: {e} ({processing_time})")

            return {
                'success': False,
                'stage': 'OCR Processing',
                'model': 'Gemini 2.5 Flash',
                'processing_time': processing_time,
                'error': str(e),
                'fallback_used': True
            }

    def _execute_stage2_generation(self, face_image_path: str, card_info: Dict,
                                 prompt_template: str) -> Dict:
        """
        Execute Stage 2: AI Image Generation with Gemini 2.0 Flash Preview

        Features:
        - Multimodal input (prompt + OCR data + face image)
        - Multiple image variants generation
        - Proper metadata and session management
        - Error handling with fallback mechanisms
        """

        print(f"\n🎨 STAGE 2: AI Image Generation (Gemini 2.0 Flash Preview)")
        print(f"   Face Image: {Path(face_image_path).name}")
        print(f"   Style Template: {prompt_template}")
        print(f"   Person: {card_info.get('name', 'Unknown')}")

        stage2_start_time = time.time()

        try:
            # Validate inputs
            if not Path(face_image_path).exists():
                raise FileNotFoundError(f"Face image not found: {face_image_path}")

            # Prepare AI configuration
            ai_config = self.config.copy()
            ai_config['prompt_template'] = prompt_template

            print(f"   🤖 AI Config: Model={ai_config['model']}, Template={prompt_template}")

            # Execute AI image generation
            print("   🎯 Generating AI images with multimodal input...")
            generation_result = self.ai_generator.generate_dollhouse_image(
                face_image_path,
                card_info,
                ai_config
            )

            if not generation_result or not generation_result.get('success'):
                raise ValueError(f"AI generation failed: {generation_result.get('error', 'Unknown error')}")

            stage2_end_time = time.time()
            processing_time = f"{stage2_end_time - stage2_start_time:.2f}s"

            result = {
                'success': True,
                'stage': 'AI Image Generation',
                'model': 'Gemini 2.0 Flash Preview Image Generation',
                'processing_time': processing_time,
                'data': generation_result,
                'metadata': {
                    'face_image_path': face_image_path,
                    'prompt_template': prompt_template,
                    'multimodal_inputs': ['text_prompt', 'face_image', 'card_info'],
                    'variants_generated': len(generation_result.get('generated_images', [])),
                    'api_model': 'gemini-2.0-flash-preview-image-generation'
                }
            }

            self.stage2_result = result

            print(f"   ✅ Stage 2 Completed: {processing_time}")
            print(f"   🖼️ Images Generated: {result['metadata']['variants_generated']}")
            print(f"   📁 Output Directory: outputs/")

            return result

        except Exception as e:
            stage2_end_time = time.time()
            processing_time = f"{stage2_end_time - stage2_start_time:.2f}s"

            print(f"   ❌ Stage 2 Failed: {e} ({processing_time})")

            return {
                'success': False,
                'stage': 'AI Image Generation',
                'model': 'Gemini 2.0 Flash Preview Image Generation',
                'processing_time': processing_time,
                'error': str(e),
                'fallback_attempted': True
            }

    def _create_error_result(self, message: str, error: str) -> Dict:
        """Create standardized error result"""

        if self.current_session:
            self.current_session['status'] = 'error'
            self.current_session['error'] = error

        return {
            'success': False,
            'error': message,
            'details': error,
            'session_id': self.current_session['session_id'] if self.current_session else None,
            'stage1_result': self.stage1_result,
            'stage2_result': self.stage2_result
        }

    def get_pipeline_status(self) -> Dict:
        """Get current pipeline status and progress"""

        if not self.current_session:
            return {'status': 'idle', 'message': 'No active pipeline session'}

        return {
            'session_id': self.current_session['session_id'],
            'status': self.current_session['status'],
            'stage1_completed': self.stage1_result is not None,
            'stage2_completed': self.stage2_result is not None,
            'current_session': self.current_session
        }