/* ========================================
   SESSION MANAGER.JS - Session management and retry logic
   Handles session management, retry functionality, and reset operations
   ======================================== */

// Retry AI Generation
function retryAIGeneration() {
    console.log('🔄 Starting AI generation retry...');
    const promptSelect = document.getElementById('promptSelect');
    if (!promptSelect || !promptSelect.value) {
        if (typeof showMessage === 'function') {
            showMessage('⚠️ Vui lòng chọn style ảnh trước khi thử lại!', 'warning');
        }
        return;
    }

    const selectedStyle = promptSelect.value;
    console.log(`🎨 Retry with style: ${selectedStyle}`);

    const retryBtn = document.getElementById('retryAIBtn');
    if (retryBtn) {
        retryBtn.disabled = true;
        retryBtn.innerHTML = '<span>⏳</span> Đang tạo ảnh AI...';
    }

    if (typeof showMessage === 'function') {
        showMessage('🎨 Đang thử lại tạo ảnh AI...', 'info');
    }

    fetch('/processing/retry_ai_generation', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ prompt_template: selectedStyle })
    })
    .then(response => response.json())
    .then(data => {
        console.log('🔄 Retry API response:', data);
        if (data.status === 'success') {
            console.log('✅ AI generation retry successful!');
            if (typeof showMessage === 'function') {
                showMessage('✅ Ảnh AI đã được tạo thành công!', 'success');
            }
            if (data.generated_images && data.generated_images.length > 0) {
                if (typeof populateGeneratedImages === 'function') {
                    populateGeneratedImages(data.generated_images);
                }
                if (typeof showImagesSectionOnly === 'function') {
                    showImagesSectionOnly();
                }
            }
            if (retryBtn) {
                retryBtn.style.display = 'none';
            }
        } else {
            console.error('❌ AI generation retry failed:', data.message);
            if (typeof showMessage === 'function') {
                showMessage(`❌ Thử lại thất bại: ${data.message}`, 'error');
            }
        }
    })
    .catch(error => {
        console.error('❌ Retry API Error:', error);
        if (typeof showMessage === 'function') {
            showMessage('❌ Lỗi kết nối API. Vui lòng thử lại.', 'error');
        }
    })
    .finally(() => {
        if (retryBtn) {
            retryBtn.disabled = false;
            retryBtn.innerHTML = '<span>🎨</span> Tạo ảnh AI tiếp theo';
        }
    });
}

// ========================================
// SIMPLE AUTO-RESET WITH LOADING BAR - DISABLED
// ========================================

// Auto-reset function - ENABLED
function startAutoResetWithLoading() {
    console.log('🔄 Starting auto-reset with loading bar...');

    const loadingElement = document.getElementById('autoResetLoading');
    const countdownElement = document.getElementById('countdownSeconds');
    const loadingBar = document.getElementById('loadingBar');

    if (!loadingElement || !countdownElement || !loadingBar) {
        console.error('❌ Loading elements not found:', {
            loadingElement: !!loadingElement,
            countdownElement: !!countdownElement,
            loadingBar: !!loadingBar
        });
        return;
    }

    // Show loading UI
    loadingElement.style.display = 'block';
    console.log('✅ Loading UI shown');

    let remainingSeconds = 10;
    let progress = 0;

    const interval = setInterval(() => {
        remainingSeconds--;
        progress += 10; // 10% per second

        // Update countdown
        countdownElement.textContent = remainingSeconds;

        // Update loading bar
        loadingBar.style.width = progress + '%';

        console.log(`🔄 Auto-reset: ${remainingSeconds}s remaining, ${progress}% complete`);

        if (remainingSeconds <= 0) {
            clearInterval(interval);
            console.log('🔄 Auto-reset completed, clearing session and reloading page...');

            // Clear session first, then reload
            fetch('/api/clear_session', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'}
            })
            .then(response => response.json())
            .then(data => {
                console.log('✅ Session cleared:', data);
                clearSessionUI();

                // Reload page after session cleared
                const url = new URL(window.location.href);
                url.searchParams.set('t', Date.now());
                window.location.href = url.toString();
            })
            .catch(error => {
                console.error('❌ Error clearing session:', error);
                // Still reload even if clear session fails
                const url = new URL(window.location.href);
                url.searchParams.set('t', Date.now());
                window.location.href = url.toString();
            });
        }
    }, 1000);
}

// Clear session on reload page
function clearSessionOnReload() {
    fetch('/api/clear_session', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'}
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            clearSessionUI();
        }
    })
    .catch(error => {});
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    clearSessionOnReload();
    // Expose auto-reset to global scope for safety
    window.startAutoResetWithLoading = startAutoResetWithLoading;
});

// Get current session state
function getCurrentSessionState() {
    return fetch('/api/session_status')
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success' && data.session) {
                return {
                    sessionId: data.session.session_id,
                    hasCardInfo: !!(data.session.card_info && Object.keys(data.session.card_info).length > 0),
                    hasGeneratedImages: !!(data.session.generated_images && data.session.generated_images.length > 0),
                    imageCount: data.session.generated_images ? data.session.generated_images.length : 0,
                    timestamp: Date.now()
                };
            }
            return null;
        })
        .catch(error => {
            console.error('❌ Error getting session state:', error);
            return null;
        });
}

// Check if session is active
function isSessionActive() {
    return getCurrentSessionState()
        .then(state => !!state)
        .catch(() => false);
}

// Clear session data from UI
function clearSessionUI() {
    console.log('🧹 Clearing session UI data...');
    const cardFields = ['cardName', 'cardTitle', 'cardCompany', 'cardEmail', 'cardPhone', 'cardAddress'];
    cardFields.forEach(fieldId => {
        const element = document.getElementById(fieldId);
        if (element) {
            element.textContent = 'No data yet';
            element.style.color = 'var(--color-text-secondary)';
            element.style.fontStyle = 'normal';
        }
    });

    const imagesContainer = document.getElementById('generatedImagesDisplay');
    if (imagesContainer) {
        imagesContainer.innerHTML = `
            <div class="empty-state">
                <div class="empty-state-icon">🖼️</div>
                <p>Chưa có ảnh AI nào được tạo</p>
                <p style="font-size: 14px; margin-top: var(--spacing-sm);">Chụp ảnh và tạo AI để xem kết quả</p>
            </div>
        `;
    }
    console.log('✅ Session UI cleared');
}

function clearSession() {
    return fetch('/api/clear_session', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'}
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            clearSessionUI();
        }
        return data;
    });
}

// Export functions to global scope
window.retryAIGeneration = retryAIGeneration;
window.getCurrentSessionState = getCurrentSessionState;
window.isSessionActive = isSessionActive;
window.clearSessionUI = clearSessionUI;
window.clearSession = clearSession;
window.clearSessionOnReload = clearSessionOnReload;
