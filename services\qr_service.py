#!/usr/bin/env python3

import os
import qrcode
import uuid
from pathlib import Path
from typing import Dict, Optional, Any
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class QRService:
    
    def __init__(self, output_dir: str = "outputs/qr_codes"):

        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Cấu hình QR code
        self.qr_config = {
            'version': 1,
            'error_correction': qrcode.constants.ERROR_CORRECT_L,
            'box_size': 10,
            'border': 4,
        }
        
        logger.info(f"✅ QR Service initialized - Output directory: {self.output_dir}")
    
    # def generate_customer_code(self, customer_data: Dict[str, Any]) -> str:

    #     try:
    #         # Tạo mã từ tên và timestamp
    #         name = customer_data.get('name', 'Unknown')
    #         timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            
    #         # <PERSON><PERSON><PERSON> sạch tên (loại bỏ dấu, kho<PERSON>ng trắng)
    #         clean_name = ''.join(c for c in name if c.isalnum())[:6].upper()
            
    #         # Tạo mã duy nhất
    #         customer_code = f"{clean_name}_{timestamp}"
            
    #         logger.info(f"✅ Generated customer code: {customer_code}")
    #         return customer_code
            
    #     except Exception as e:
    #         logger.error(f"❌ Error generating customer code: {e}")
    #         # Fallback: tạo mã random
    #         return f"CUST_{str(uuid.uuid4())[:8].upper()}"
    
    def create_checkin_id(self, customer_data: Dict[str, Any], customer_id: int = None) -> str:

        try:
            # Chỉ lưu checkin_id đơn giản
            if customer_id:
                checkin_id = f"FCB25RO{customer_id:04d}"
                logger.info(f"✅ Created checkin_id: {checkin_id}")
                return checkin_id
            else:
                logger.warning("⚠️ No customer_id provided")
                return "NO_ID"
            
        except Exception as e:
            logger.error(f"❌ Error creating checkin_id: {e}")
            return "ERROR"
    
    def create_customer_url(self, customer_id: int, base_url: str = "https://your-domain.com") -> str:

        return f"{base_url}/customer/{customer_id}"
    
    def generate_qr_code(self, data: str, filename: str = None, 
                        qr_type: str = "text") -> Optional[str]:
  
        try:
            # Tạo QR code
            qr = qrcode.QRCode(**self.qr_config)
            qr.add_data(data)
            qr.make(fit=True)
            
            # Tạo image
            qr_image = qr.make_image(fill_color="black", back_color="white")
            
            # Tạo tên file nếu chưa có
            if filename is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"qr_{qr_type}_{timestamp}.png"
            
            # Đảm bảo file có extension .png
            if not filename.endswith('.png'):
                filename += '.png'
            
            # Lưu file
            filepath = self.output_dir / filename
            qr_image.save(filepath)
            
            logger.info(f"✅ QR code generated: {filepath}")
            return str(filepath)
            
        except Exception as e:
            logger.error(f"❌ Error generating QR code: {e}")
            return None
    
    def generate_customer_qr(self, customer_data: Dict[str, Any], 
                           customer_id: int = None, 
                           qr_format: str = "vcard") -> Optional[tuple[str, str]]:
 
        try:
            customer_name = customer_data.get('name', 'Unknown')
            
            # Tạo dữ liệu QR theo format
            if qr_format == "vcard":
                qr_data = self.create_checkin_id(customer_data, customer_id)
                qr_type = "checkin"
            elif qr_format == "url" and customer_id:
                qr_data = self.create_customer_url(customer_id)
                qr_type = "url"
            else:
                # Format text: chỉ lưu checkin_id đơn giản
                if customer_id:
                    qr_data = f"FCB25RO{customer_id:04d}"
                else:
                    qr_data = "NO_ID"
                qr_type = "text"
            
            # Tạo tên file
            clean_name = ''.join(c for c in customer_name if c.isalnum())[:10]
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{clean_name}_{qr_type}_{timestamp}.png"
            
            # Tạo QR code
            qr_path = self.generate_qr_code(qr_data, filename, qr_type)
            
            if qr_path:
                logger.info(f"✅ Customer QR generated for {customer_name}: {qr_path}")
                return qr_path, qr_data
            else:
                logger.error(f"❌ Failed to generate QR for customer: {customer_name}")
                return None
                
        except Exception as e:
            logger.error(f"❌ Error generating customer QR: {e}")
            return None
    
    
    # def cleanup_old_qr_files(self, keep_days: int = 30) -> int:
   
    #     try:
    #         import time
    #         current_time = time.time()
    #         cutoff_time = current_time - (keep_days * 24 * 60 * 60)
            
    #         cleaned_count = 0
    #         for qr_file in self.output_dir.glob("*.png"):
    #             if qr_file.stat().st_mtime < cutoff_time:
    #                 try:
    #                     qr_file.unlink()
    #                     cleaned_count += 1
    #                     logger.info(f"🗑️ Deleted old QR file: {qr_file.name}")
    #                 except Exception as e:
    #                     logger.warning(f"⚠️ Could not delete file: {qr_file.name}, error: {e}")
            
    #         if cleaned_count > 0:
    #             logger.info(f"✅ Cleaned up {cleaned_count} old QR files")
            
    #         return cleaned_count
            
    #     except Exception as e:
    #         logger.error(f"❌ Error cleaning up QR files: {e}")
    #         return 0
    
    # def get_qr_stats(self) -> Dict[str, Any]:

    #     try:
    #         qr_files = list(self.output_dir.glob("*.png"))
    #         total_size = sum(f.stat().st_size for f in qr_files)
            
    #         return {
    #             'total_files': len(qr_files),
    #             'total_size_bytes': total_size,
    #             'total_size_mb': round(total_size / (1024 * 1024), 2),
    #             'output_directory': str(self.output_dir)
    #         }
    #     except Exception as e:
    #         logger.error(f"❌ Error getting QR stats: {e}")
    #         return {}


# Tạo instance global
qr_service = QRService()

# Các function tiện ích
def generate_customer_qr(customer_data: Dict[str, Any], 
                        customer_id: int = None, 
                        qr_format: str = "vcard") -> Optional[tuple[str, str]]:

    return qr_service.generate_customer_qr(customer_data, customer_id, qr_format)


# def cleanup_qr_files(keep_days: int = 30) -> int:

#     return qr_service.cleanup_old_qr_files(keep_days)

# def get_qr_service_stats() -> Dict[str, Any]:

#     return qr_service.get_qr_stats()

def generate_checkin_id(customer_id: int) -> str:

    return f"FCB25RO{customer_id:04d}"

