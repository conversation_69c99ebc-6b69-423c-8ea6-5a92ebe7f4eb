#!/usr/bin/env python3


import os
import sys
import time
from pathlib import Path
from typing import Dict, Optional, List, Tuple
from datetime import datetime
import logging

try:
    from PIL import Image, ImageDraw, ImageFont
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False
    print("⚠️ PIL chưa được cài đặt. Chạy: pip install Pillow")

try:
    from reportlab.pdfgen import canvas
    from reportlab.lib.pagesizes import A4
    from reportlab.lib.units import mm
    from reportlab.graphics import renderPDF
    from reportlab.graphics.shapes import Drawing
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False
    print("⚠️ reportlab chưa được cài đặt. Chạy: pip install reportlab")

try:
    import win32print
    import win32ui
    from win32.lib import win32con
    WIN32_AVAILABLE = True
except ImportError:
    WIN32_AVAILABLE = False
    print("⚠️ pywin32 chưa được cài đặt. Chạy: pip install pywin32")

logger = logging.getLogger(__name__)

class PrintService:
# Xprinter 350BM
    
    def __init__(self, printer_name: str = "Xprinter XP-350BM", pdf_output_dir: str = "outputs/labels_pdf"):

        self.printer_name = printer_name
        self.pdf_output_dir = Path(pdf_output_dir)
        self.pdf_output_dir.mkdir(parents=True, exist_ok=True)
        
        self.label_width = 80  # 8cm = 80mm (ngang)
        self.label_height = 50  # 5cm = 50mm (cao)
        
        # DPI settings (dots per inch)
        self.dpi = 203  # Xprinter 350BM thường có DPI 203
        self.pixels_per_mm = self.dpi / 25.4  # Convert mm to pixels
        
        # Label dimensions in pixels
        self.label_width_px = int(self.label_width * self.pixels_per_mm)
        self.label_height_px = int(self.label_height * self.pixels_per_mm)
        
        # Margins (in mm)
        self.margin_top = 2
        self.margin_bottom = 2
        self.margin_left = 2
        self.margin_right = 2
        
        # Đăng ký font Noto Sans CJK JP nếu có
        self._register_noto_fonts()
        
        # Content area
        self.content_width = self.label_width - self.margin_left - self.margin_right
        self.content_height = self.label_height - self.margin_top - self.margin_bottom
    
    def _register_noto_fonts(self):
        try:
            fonts_dir = Path("fonts")
            if fonts_dir.exists():
                # Đăng ký font Regular
                regular_font_path = fonts_dir / "NotoSans-Regular.ttf"
                if regular_font_path.exists():
                    pdfmetrics.registerFont(TTFont('NotoSans', str(regular_font_path)))
                    logger.info("✅ Đã đăng ký font Noto Sans Regular")
                
                # Đăng ký font Bold
                bold_font_path = fonts_dir / "NotoSans-Bold.ttf"
                if bold_font_path.exists():
                    pdfmetrics.registerFont(TTFont('NotoSans-Bold', str(bold_font_path)))
                    logger.info("✅ Đã đăng ký font Noto Sans Bold")
                
                self.noto_font_available = True
            else:
                self.noto_font_available = False
                logger.warning("⚠️ Thư mục fonts không tồn tại")
        except Exception as e:
            self.noto_font_available = False
            logger.warning(f"⚠️ Không thể đăng ký font Noto Sans: {e}")
        
        # Font settings
        self.font_size_large = 12
        self.font_size_medium = 10
        self.font_size_small = 8
        
        logger.info(f"✅ Print Service initialized")
        logger.info(f"   Printer: {self.printer_name}")
        logger.info(f"   Label size: {self.label_width}mm x {self.label_height}mm")
        logger.info(f"   Label pixels: {self.label_width_px} x {self.label_height_px}")
    
    def check_printer_available(self) -> bool:

        if not WIN32_AVAILABLE:
            logger.warning("⚠️ win32print not available")
            return False
            
        try:
            printers = [printer[2] for printer in win32print.EnumPrinters(win32print.PRINTER_ENUM_LOCAL)]
            
            if self.printer_name in printers:
                logger.info(f"✅ Printer '{self.printer_name}' is available")
                return True
            else:
                logger.warning(f"⚠️ Printer '{self.printer_name}' not found")
                logger.info(f"Available printers: {printers}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error checking printer: {e}")
            return False
    
    def _check_printer_available(self, printer_name: str) -> bool:
        """Kiểm tra máy in cụ thể có sẵn không"""
        try:
            printers = [printer[2] for printer in win32print.EnumPrinters(win32print.PRINTER_ENUM_LOCAL)]
            return printer_name in printers
        except Exception as e:
            logger.error(f"❌ Error checking printer: {e}")
            return False
    
    
    def _generate_customer_code(self, customer_data: Dict[str, str], customer_id: int = None) -> str:

        try:
            if customer_id:
                # Sử dụng checkin_id format: FCB25RO + customer_id với padding 4 chữ số
                return f"FCB25RO{customer_id:04d}"
            else:
                # Fallback: tạo mã từ tên và timestamp
                name = customer_data.get('name', 'Unknown')
                timestamp = datetime.now().strftime("%m%d%H%M")
                clean_name = ''.join(c for c in name if c.isalnum())[:3].upper()
                return f"{clean_name}{timestamp[-4:]}"
            
        except Exception as e:
            logger.error(f"❌ Error generating customer code: {e}")
            return "FCB25RO0001"
    
    def create_label_pdf(self, customer_data: Dict[str, str], qr_image_path: str, 
                        copies: int = 1, customer_id: int = None) -> Optional[str]:

        if not REPORTLAB_AVAILABLE:
            logger.error("❌ reportlab not available")
            return None
            
        try:
            # Tạo tên file PDF
            customer_name = customer_data.get('name', 'Unknown')
            clean_name = ''.join(c for c in customer_name if c.isalnum())[:10]
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            pdf_filename = f"label_{clean_name}_{timestamp}.pdf"
            pdf_path = self.pdf_output_dir / pdf_filename
            
            # Tạo PDF với encoding UTF-8
            c = canvas.Canvas(str(pdf_path), pagesize=(self.label_width * mm, self.label_height * mm))
            
            # Vẽ border cho label
            c.setStrokeColor('black')
            c.setLineWidth(0.5)
            c.rect(0, 0, self.label_width * mm, self.label_height * mm)
            
            # Vẽ thông tin khách hàng (layout giống ảnh)
            current_y = self.label_height * mm - 5 * mm
            
            # 1. Công ty (bên trái, font lớn, có thể xuống dòng)
            company = customer_data.get('company', '').upper()
            if company:
                # Sử dụng font Noto Sans nếu có, fallback về Courier-Bold
                if hasattr(self, 'noto_font_available') and self.noto_font_available:
                    c.setFont("NotoSans-Bold", 11)
                else:
                    c.setFont("Courier-Bold", 11)
                # Kiểm tra nếu tên công ty quá dài, chia thành 2 dòng
                # Sử dụng font tương ứng để tính width
                font_name = "NotoSans-Bold" if (hasattr(self, 'noto_font_available') and self.noto_font_available) else "Courier-Bold"
                company_width = c.stringWidth(company, font_name, 11)
                max_width = (self.label_width * mm) * 0.85  # 85% chiều rộng label để tránh chồng lên QR
                
                if company_width > max_width:
                    # Chia tên công ty thành 2 dòng thông minh hơn
                    words = company.split()
                    line1 = ""
                    line2 = ""
                    
                    # Tìm điểm chia tốt nhất (ưu tiên chia ở giữa)
                    mid_point = len(words) // 2
                    
                    # Thử chia ở giữa trước
                    line1_words = words[:mid_point]
                    line2_words = words[mid_point:]
                    
                    line1 = " ".join(line1_words)
                    line2 = " ".join(line2_words)
                    
                    # Kiểm tra nếu dòng 1 vẫn quá dài, chia lại
                    if c.stringWidth(line1, font_name, 11) > max_width:
                        # Chia từ đầu đến khi vừa
                        line1 = ""
                        line2 = ""
                        for word in words:
                            test_line = line1 + " " + word if line1 else word
                            if c.stringWidth(test_line, font_name, 11) <= max_width:
                                line1 = test_line
                            else:
                                line2 = word
                                break
                        # Thêm các từ còn lại vào dòng 2
                        remaining_words = words[words.index(word):]
                        if remaining_words:
                            line2 = " ".join(remaining_words)
                    
                    # Vẽ dòng 1
                    c.drawString(2 * mm, current_y, line1)
                    current_y -= 5 * mm
                    
                    # Vẽ dòng 2 nếu có
                    if line2:
                        c.drawString(2 * mm, current_y, line2)
                        current_y -= 9 * mm
                else:
                    # Vẽ 1 dòng
                    c.drawString(2 * mm, current_y, company)
                    current_y -= 5 * mm
            
            # 2. Tên khách hàng (bên trái, font lớn)
            name = customer_data.get('name', '').upper()
            if name:
                # Sử dụng font Noto Sans nếu có, fallback về Courier-Bold
                if hasattr(self, 'noto_font_available') and self.noto_font_available:
                    c.setFont("NotoSans-Bold", 12)
                else:
                    c.setFont("Courier-Bold", 12)
                c.drawString(2 * mm, current_y, name)
                current_y -= 6 * mm
            
            # 3. QR Code (góc dưới bên phải)
            if os.path.exists(qr_image_path):
                qr_size = 24 * mm  # Kích thước QR code lớn hơn để giống ảnh
                qr_x = self.label_width * mm - 3 * mm - qr_size  # Bên phải với margin 3mm
                qr_y = 8 * mm  # Đẩy lên cao hơn để có chỗ cho checkin_id
                
                # Vẽ QR code
                c.drawImage(qr_image_path, qr_x, qr_y, width=qr_size, height=qr_size)
                
                # Mã khách hàng dưới QR code (căn giữa theo chiều ngang)
                customer_code = self._generate_customer_code(customer_data, customer_id)
                # Hiển thị full checkin_id
                full_checkin_id = customer_code
                # Sử dụng font Noto Sans nếu có, fallback về Courier
                if hasattr(self, 'noto_font_available') and self.noto_font_available:
                    c.setFont("NotoSans", 7)  # Font nhỏ hơn để vừa
                else:
                    c.setFont("Courier", 7)
                code_width = c.stringWidth(full_checkin_id, "NotoSans" if (hasattr(self, 'noto_font_available') and self.noto_font_available) else "Courier", 7)
                code_x = qr_x + (qr_size - code_width) / 2  # Căn giữa theo chiều ngang QR
                code_y = qr_y - 2 * mm  # Dưới QR code với khoảng cách 4mm
                c.drawString(code_x, code_y, full_checkin_id)
            
            # Hoàn thành trang
            c.showPage()
            c.save()
            
            logger.info(f"✅ Label PDF created: {pdf_path}")
            return str(pdf_path)
            
        except Exception as e:
            logger.error(f"❌ Error creating label PDF: {e}")
            return None
    
    def print_pdf(self, pdf_path: str, copies: int = 1) -> bool:

        if not WIN32_AVAILABLE:
            logger.error("❌ win32print not available")
            return False
            
        if not self.check_printer_available():
            logger.error("❌ Printer not available")
            return False
            
        try:
            # Sử dụng lệnh in PDF qua Windows
            import subprocess
            
            for copy in range(copies):
                # Lệnh in PDF
                cmd = [
                    'powershell', '-Command',
                    f'Start-Process -FilePath "{pdf_path}" -Verb Print -WindowStyle Hidden'
                ]
                
                result = subprocess.run(cmd, capture_output=True, text=True)
                
                if result.returncode == 0:
                    logger.info(f"✅ PDF sent to printer (copy {copy + 1})")
                    if copy < copies - 1:
                        time.sleep(1)  # Delay giữa các bản in
                else:
                    logger.error(f"❌ Failed to print PDF (copy {copy + 1}): {result.stderr}")
                    return False
            
            logger.info(f"✅ Successfully printed {copies} PDF copies")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error printing PDF: {e}")
            return False
    
    def print_photo(self, image_path: str, copies: int = 1, printer_name: str = None) -> bool:
        """
        In ảnh trực tiếp sử dụng win32print
        Print photo directly using win32print
        
        Args:
            image_path: Đường dẫn ảnh
            copies: Số bản in
            printer_name: Tên máy in (nếu None thì dùng printer mặc định)
        
        Returns:
            bool: True nếu in thành công, False nếu thất bại
        """
        try:
            if not WIN32_AVAILABLE:
                logger.error("❌ win32print not available - cannot print photo")
                return False
            
            if not PIL_AVAILABLE:
                logger.error("❌ PIL not available - cannot process image")
                return False
            
            # Kiểm tra file ảnh tồn tại
            if not os.path.exists(image_path):
                logger.error(f"❌ Image file not found: {image_path}")
                return False
            
            # Mở ảnh bằng PIL
            try:
                image = Image.open(image_path)
                logger.info(f"✅ Image loaded: {image.size[0]}x{image.size[1]} pixels")
            except Exception as e:
                logger.error(f"❌ Failed to open image: {e}")
                return False
            
            # Sử dụng printer_name từ parameter hoặc từ instance
            target_printer = printer_name or self.printer_name
            
            # In ảnh tự động không cần click (sử dụng win32print trực tiếp)
            import win32print
            import win32ui
            import win32con
            
            # In từng bản
            for copy in range(copies):
                logger.info(f"🖨️ Printing copy {copy + 1}/{copies}")
                
                try:
                    # Mở máy in
                    printer_handle = win32print.OpenPrinter(target_printer)
                    
                    try:
                        # Đọc file ảnh
                        with open(image_path, 'rb') as f:
                            image_data = f.read()
                        
                        # Tạo job in
                        job_info = {
                            'pDocument': f"AI Image Print - Copy {copy + 1}",
                            'pDatatype': 'RAW',
                            'pStatus': None,
                            'Priority': 1,
                            'Position': 0,
                            'TotalPages': 1,
                            'PagesPrinted': 0,
                            'Submitted': {
                                'dwLowDateTime': 0,
                                'dwHighDateTime': 0
                            }
                        }
                        
                        # Cài đặt in màu và khổ giấy
                        logger.info(f"📄 Using default paper size from printer settings")
                        logger.info(f"🎨 Printing in COLOR mode for AI images")
                        
                        # Bắt đầu job in với cài đặt màu
                        job_id = win32print.StartDocPrinter(
                            printer_handle, 
                            1, 
                            (f"AI Image Print - Copy {copy + 1}", None, "RAW")
                        )
                        
                        # Bắt đầu trang
                        win32print.StartPagePrinter(printer_handle)
                        
                        # Gửi dữ liệu ảnh
                        win32print.WritePrinter(printer_handle, image_data)
                        
                        # Kết thúc trang và job
                        win32print.EndPagePrinter(printer_handle)
                        win32print.EndDocPrinter(printer_handle)
                        
                        logger.info(f"✅ Successfully sent print job {job_id} for copy {copy + 1}")
                        
                    finally:
                        win32print.ClosePrinter(printer_handle)
                    
                except Exception as e:
                    logger.error(f"❌ Error printing copy {copy + 1}: {e}")
                    return False
            
            logger.info(f"✅ Successfully printed {copies} copies of photo")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error printing photo: {e}")
            return False
    
    def print_customer_labels(self, customer_data: Dict[str, str], qr_image_path: str, 
                            copies: int = 1, customer_id: int = None) -> bool:

        try:
            logger.info(f"🖨️ Starting to print {copies} labels for customer: {customer_data.get('name', 'Unknown')}")
            
            if not REPORTLAB_AVAILABLE:
                logger.error("❌ reportlab not available - cannot create PDF labels")
                return False
            
            # Tạo PDF trước, sau đó in PDF
            logger.info("📄 Creating PDF first...")
            pdf_path = self.create_label_pdf(customer_data, qr_image_path, copies, customer_id)
            
            if pdf_path:
                logger.info(f"✅ PDF created: {pdf_path}")
                
                # In PDF
                success = self.print_pdf(pdf_path, copies)
                
                if success:
                    logger.info(f"✅ Successfully printed {copies} labels from PDF")
                    return True
                else:
                    logger.error("❌ Failed to print PDF")
                    return False
            else:
                logger.error("❌ Failed to create PDF")
                return False
            
        except Exception as e:
            logger.error(f"❌ Error in print_customer_labels: {e}")
            return False
    
    def create_ai_image_pdf(self, image_path: str, copies: int = 1) -> Optional[str]:
        """
        Tạo PDF từ ảnh AI để in
        Create PDF from AI image for printing
        
        Args:
            image_path: Đường dẫn ảnh AI
            copies: Số bản in
            
        Returns:
            str: Đường dẫn file PDF đã tạo, None nếu thất bại
        """
        if not REPORTLAB_AVAILABLE:
            logger.error("❌ reportlab not available")
            return None
            
        if not PIL_AVAILABLE:
            logger.error("❌ PIL not available")
            return None
            
        try:
            # Kiểm tra file ảnh tồn tại
            if not os.path.exists(image_path):
                logger.error(f"❌ Image file not found: {image_path}")
                return None
            
            # Tạo tên file PDF
            image_name = Path(image_path).stem
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            pdf_filename = f"ai_image_{image_name}_{timestamp}.pdf"
            pdf_path = self.pdf_output_dir / pdf_filename
            
            # Mở ảnh để lấy kích thước
            with Image.open(image_path) as img:
                img_width, img_height = img.size
                
                # Tính toán kích thước PDF phù hợp với ảnh
                # Sử dụng A4 size làm base, nhưng điều chỉnh theo tỷ lệ ảnh
                max_width = A4[0] * 0.8  # 80% chiều rộng A4
                max_height = A4[1] * 0.8  # 80% chiều cao A4
                
                # Tính tỷ lệ để fit ảnh vào PDF
                width_ratio = max_width / img_width
                height_ratio = max_height / img_height
                scale_ratio = min(width_ratio, height_ratio)
                
                pdf_width = img_width * scale_ratio
                pdf_height = img_height * scale_ratio
                
                logger.info(f"📐 Image size: {img_width}x{img_height}")
                logger.info(f"📐 PDF size: {pdf_width:.1f}x{pdf_height:.1f}")
            
            # Tạo PDF với kích thước phù hợp
            c = canvas.Canvas(str(pdf_path), pagesize=(pdf_width, pdf_height))
            
            # Vẽ ảnh vào PDF
            c.drawImage(image_path, 0, 0, width=pdf_width, height=pdf_height)
            
            # Hoàn thành trang
            c.showPage()
            c.save()
            
            logger.info(f"✅ AI Image PDF created: {pdf_path}")
            return str(pdf_path)
            
        except Exception as e:
            logger.error(f"❌ Error creating AI image PDF: {e}")
            return None
    
    # def save_label_preview(self, customer_data: Dict[str, str], qr_image_path: str, 
    #                       output_path: str = None, customer_id: int = None) -> Optional[str]:

    #     try:
    #         if not REPORTLAB_AVAILABLE:
    #             logger.error("❌ reportlab not available - cannot create PDF preview")
    #             return None
            
    #         # Tạo PDF preview
    #         pdf_path = self.create_label_pdf(customer_data, qr_image_path, 1, customer_id)
            
    #         if pdf_path:
    #             logger.info(f"✅ Label preview saved: {pdf_path}")
    #             return pdf_path
    #         else:
    #             logger.error("❌ Failed to create PDF preview")
    #             return None
            
    #     except Exception as e:
    #         logger.error(f"❌ Error saving label preview: {e}")
    #         return None
    
    # def get_printer_status(self) -> Dict[str, any]:
 
    #     try:
    #         if not WIN32_AVAILABLE:
    #             return {'available': False, 'error': 'win32print not available'}
            
    #         printers = [printer[2] for printer in win32print.EnumPrinters(win32print.PRINTER_ENUM_LOCAL)]
            
    #         return {
    #             'available': self.printer_name in printers,
    #             'printer_name': self.printer_name,
    #             'all_printers': printers,
    #             'label_size_mm': f"{self.label_width}x{self.label_height}",
    #             'label_size_px': f"{self.label_width_px}x{self.label_height_px}",
    #             'dpi': self.dpi
    #         }
            
    #     except Exception as e:
    #         logger.error(f"❌ Error getting printer status: {e}")
    #         return {'available': False, 'error': str(e)}


# Tạo instance global
print_service = PrintService()

# Các function tiện ích
def print_customer_labels(customer_data: Dict[str, str], qr_image_path: str, 
                         copies: int = 1, customer_id: int = None) -> bool:

    return print_service.print_customer_labels(customer_data, qr_image_path, copies, customer_id)

# def create_label_preview(customer_data: Dict[str, str], qr_image_path: str, 
#                         output_path: str = None, customer_id: int = None) -> Optional[str]:

#     return print_service.save_label_preview(customer_data, qr_image_path, output_path, customer_id)

# def check_printer_status() -> Dict[str, any]:

#     return print_service.get_printer_status()

# def create_label_pdf_only(customer_data: Dict[str, str], qr_image_path: str) -> Optional[str]:

#     return print_service.create_label_pdf(customer_data, qr_image_path, copies=1)

def print_ai_image(image_path: str, copies: int = 1, printer_name: str = "") -> bool:
    """
    In 1 ảnh AI sử dụng Photo
    Print single AI image using Photo
    
    Args:
        image_path: Đường dẫn ảnh AI
        copies: Số bản in
        printer_name: Tên máy in
    
    Returns:
        bool: True nếu in thành công, False nếu thất bại
    """
    try:
        logger.info(f"🖨️ Starting to print AI image: {Path(image_path).name} with {copies} copies on printer: {printer_name}")
        
        if not PIL_AVAILABLE:
            logger.error("❌ PIL not available - cannot process images")
            return False
            
        if not WIN32_AVAILABLE:
            logger.error("❌ win32print not available - cannot print")
            return False
        
        # Kiểm tra file ảnh tồn tại
        if not os.path.exists(image_path):
            logger.error(f"❌ Image file not found: {image_path}")
            return False
        
        # Mở ảnh bằng PIL
        try:
            image = Image.open(image_path)
            logger.info(f"✅ Image loaded: {image.size[0]}x{image.size[1]} pixels")
        except Exception as e:
            logger.error(f"❌ Failed to open image: {e}")
            return False
        
        # In ảnh sử dụng Photo
        printer_service = PrintService(printer_name=printer_name)
        success = printer_service.print_photo(image_path, copies, printer_name)
        
        if success:
            logger.info(f"✅ Successfully printed AI image: {Path(image_path).name}")
            return True
        else:
            logger.error(f"❌ Failed to print AI image: {Path(image_path).name}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error in print_ai_image: {e}")
        return False
