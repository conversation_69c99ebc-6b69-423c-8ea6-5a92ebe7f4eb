"""
Session Model - Quản lý phiên làm việc và dữ liệu đã chụp
Manage user sessions and captured data
"""

import os  # Thư viện hệ điều hành
import json  # Thư viện xử lý JSON
import uuid  # Thư viện tạo ID duy nhất
from datetime import datetime  # Thư viện ngày tháng
from pathlib import Path  # Thư viện xử lý đường dẫn
import pickle  # Th<PERSON> viện serialize dữ liệu Python
from typing import List, Dict  # Thư viện type hints
from dotenv import load_dotenv

load_dotenv()

# Import file manager để lưu file có tổ chức - Import file manager for organized file saving
try:
    from utils.file_manager import file_manager
except ImportError:
    # Fallback khi chạy file riêng lẻ - Fallback when running file standalone
    file_manager = None
    print("⚠️ file_manager not available (running standalone)")


class SessionModel:
    """Model quản lý phiên làm việc người dùng và dữ liệu đã chụp - Model for managing user sessions and captured data"""

    def __init__(self):
        """Khởi tạo Session Model - với session persistence nhưng có thể clear"""
        # Session với persistence nhưng có thể được clear khi cần
        self.current_session = None  # Session hiện tại
        self.session_file_path = 'sessions/current_session.pkl'  # File lưu session

        # Tạo thư mục sessions nếu chưa có
        os.makedirs('sessions', exist_ok=True)

        # KHÔNG tự động khôi phục session - để clearSessionOnReload() hoạt động
        print("📁 Session Model initialized with conditional persistence")

    def create_session(self):
        """Tạo session mới (chỉ trong bộ nhớ) - Create a new session (in-memory only)"""
        session_id = str(uuid.uuid4())  # Tạo ID duy nhất cho session

        # Dữ liệu session mới - New session data
        session_data = {
            'session_id': session_id,  # ID phiên làm việc
            'created_at': datetime.now().isoformat(),  # Thời gian tạo
            'status': 'active',  # Trạng thái phiên
            'card_captured': False,  # Đã chụp name card chưa
            'face_captured': False,  # Đã chụp khuôn mặt chưa
            'card_image': None,  # Đường dẫn ảnh name card
            'face_image': None,  # Đường dẫn ảnh khuôn mặt
            'card_info': None,  # Thông tin đã trích xuất từ name card
            'generated_images': []  # Danh sách ảnh AI đã tạo
        }

        self.current_session = session_data  # Lưu session hiện tại

        # print(f"✅ New session created: {session_id}")
        return session_id  # Trả về ID session

    def load_session(self, session_id):
        if not session_id:
            return False

        if self.current_session and self.current_session.get('session_id') == session_id:
            return True

        self.current_session = {
            'session_id': session_id,
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat(),
            'card_captured': False,
            'face_captured': False,
            'card_image': None,
            'face_image': None,
            'card_info': {},
            'generated_images': [],
            'status': 'active'
        }
        return True

    def save_session_data(self):
        """Lưu dữ liệu session vào file để persistence"""
        try:
            if self.current_session:
                with open(self.session_file_path, 'wb') as f:
                    pickle.dump(self.current_session, f)
                # print(f"💾 Session saved to file: {self.session_file_path}")
            return True
        except Exception as e:
            print(f"❌ Error saving session to file: {e}")
            return False

    def clear_session(self):
        """Xóa session và file lưu trữ - Reset hoàn toàn cho workflow mới"""
        print("🗑️ Clearing session for fresh workflow...")
        self.current_session = None
        try:
            if os.path.exists(self.session_file_path):
                os.remove(self.session_file_path)
                print(f"🗑️ Session file deleted: {self.session_file_path}")
        except Exception as e:
            print(f"❌ Error deleting session file: {e}")
        print("✅ Session cleared completely - Ready for new workflow")
        return True

    def update_session(self, **kwargs):
        """Cập nhật dữ liệu session - Update session data"""
        if not self.current_session:  # Nếu không có session hiện tại
            return False  # Trả về False

        # Lặp qua các tham số được truyền vào
        for key, value in kwargs.items():
            if key in self.current_session:  # Nếu key tồn tại trong session
                self.current_session[key] = value  # Cập nhật giá trị

        self.current_session['updated_at'] = datetime.now().isoformat()  # Cập nhật thời gian sửa đổi
        return self.save_session_data()  # Lưu session data

    def save_captured_image(self, image_path, image_type):
        """Lưu tham chiếu ảnh đã chụp và tạo/cập nhật checkin record trong database"""
        if not self.current_session:  # Nếu không có session hiện tại
            return None  # Trả về None

        try:
            # Cập nhật dữ liệu session với đường dẫn ảnh gốc
            if image_type == 'card':  # Nếu là ảnh name card
                self.current_session['card_captured'] = True  # Đánh dấu đã chụp card
                self.current_session['card_image'] = str(image_path)  # Lưu đường dẫn ảnh card
            elif image_type == 'face':  # Nếu là ảnh khuôn mặt
                self.current_session['face_captured'] = True  # Đánh dấu đã chụp face
                self.current_session['face_image'] = str(image_path)  # Lưu đường dẫn ảnh face

            # Tạo hoặc cập nhật checkin record trong database
            self._create_or_update_checkin_record()

            self.save_session_data()  # Lưu session data
            print(f"✅ {image_type.title()} image reference saved: {image_path}")  # Log thành công
            return str(image_path)  # Trả về đường dẫn ảnh

        except Exception as e:
            print(f"❌ Error saving {image_type} image reference: {e}")  # Log lỗi
            return None  # Trả về None nếu có lỗi

    def _create_or_update_checkin_record(self):
        """Tạo hoặc cập nhật checkin record trong database với thông tin ảnh hiện tại"""
        try:
            from database.db_service import db_service

            checkin_id = self.current_session.get('db_checkin_id')

            if checkin_id:
                updated_checkin = db_service.checkin_crud.update_images(
                    checkin_id=checkin_id,
                    card_img=self.current_session.get('card_image'),
                    face_img=self.current_session.get('face_image'),
                    ai_img=self._get_first_ai_image_path()
                )
                if updated_checkin:
                    print(f"✅ Updated checkin record (ID: {checkin_id}) with new image")
                    return True
                else:
                    print(f"❌ Failed to update checkin record (ID: {checkin_id})")
                    return False
            else:
                new_checkin = db_service.checkin_crud.create_checkin(
                    customer_id=None,
                    history_id=None,
                    card_img=self.current_session.get('card_image'),
                    face_img=self.current_session.get('face_image'),
                    ai_img=self._get_first_ai_image_path()
                )
                if new_checkin:
                    self.current_session['db_checkin_id'] = new_checkin.checkin_id
                    print(f"✅ Created new checkin record (ID: {new_checkin.checkin_id})")
                    return True
                else:
                    print("❌ Failed to create checkin record")
                    return False

        except Exception as e:
            print(f"❌ Error creating/updating checkin record: {e}")
            return False

    def _get_first_ai_image_path(self):
        """Lấy đường dẫn ảnh AI đầu tiên từ generated_images - Get first AI image path from generated_images"""
        try:
            generated_images = self.current_session.get('generated_images', [])
            if generated_images and len(generated_images) > 0:
                return generated_images[0].get('path')
            return None
        except Exception as e:
            print(f"⚠️ Error getting first AI image path: {e}")
            return None

    def save_ocr_only(self, card_info):
        """Chỉ lưu OCR data vào session (không tạo QR, không in label) - Save OCR data to session only (no QR generation, no printing)"""
        if not self.current_session: 
            print("ERROR No current session")
            return False

        try:
            print(f"🔍 Saving OCR data to session only: {card_info}")
            self.current_session['card_info'] = card_info
            self.save_session_data()
            print(f"✅ OCR data saved to session")
            return True
        except Exception as e:
            print(f"❌ Error saving OCR data: {e}")
            return False

    def save_card_info(self, card_info):
        """Lưu thông tin card đã trích xuất vào session và file có tổ chức - Save extracted card information to session and organized files"""
        if not self.current_session: 
            print("ERROR No current session")
            return False  

        try:
            print(f"🔍 Saving card_info to session: {card_info}")
            self.current_session['card_info'] = card_info  


            try:
                from database.db_service import db_service

                session_data = {
                    'card_image_path': self.current_session.get('card_image'),  
                    'face_image_path': self.current_session.get('face_image'),  
                    'ai_image_path': self._get_first_ai_image_path(),  
                    'existing_checkin_id': self.current_session.get('db_checkin_id')  
                }

                # Lưu vào database
                db_result = db_service.save_card_info_to_db(card_info, session_data)

                if db_result:
                    # Lưu kết quả vào session
                    self.current_session['db_customer_id'] = db_result['customer_id']
                    self.current_session['db_action'] = db_result['action']
                    if db_result.get('history_id'):
                        self.current_session['db_history_id'] = db_result['history_id']
                    if db_result.get('checkin_id'):
                        self.current_session['db_checkin_id'] = db_result['checkin_id']

                    # Logic cập nhật customer_id đã được xử lý trong database service

                    print(f"✅ Card info saved to database: Customer ID {db_result['customer_id']} ({db_result['action']})")
                    if db_result.get('checkin_id'):
                        print(f"✅ Checkin ID: {db_result['checkin_id']}")
                    
                    # Tạo QR code và in label
                    try:
                        from services.qr_service import generate_customer_qr
                        
                        customer_id = db_result['customer_id']
                        if customer_id and db_result.get('customer'):
                            customer_obj = db_result['customer']
                            customer_data = {
                                'name': getattr(customer_obj, 'customer_name', ''),
                                'company': getattr(customer_obj, 'company_name', ''),
                                'phone': getattr(customer_obj, 'customer_tel', ''),
                                'email': getattr(customer_obj, 'customer_email', '')
                            }

                            print(f'Kiểm tra customer_data: {customer_data}')
                            
                            # Tạo QR vCard format
                            result = generate_customer_qr(
                                customer_data=customer_data,
                                customer_id=customer_id,
                                qr_format="vcard"
                            )
                            
                            if result:
                                qr_path, qr_data = result
                                self.current_session['qr_code_path'] = qr_path
                                self.current_session['qr_data'] = qr_data
                                print(f"✅ QR code generated: {qr_path}")
                                
                                # Tự động in label sau khi tạo QR thành công
                                try:
                                    from services.print_service import print_customer_labels
                                    
                                    print("🖨️ Starting automatic label printing...")
                                    print_success = print_customer_labels(
                                        customer_data=customer_data,
                                        qr_image_path=qr_path,
                                        copies=1,  # In 1 label
                                        customer_id=customer_id
                                    )
                                    
                                    if print_success:
                                        self.current_session['labels_printed'] = True
                                        print("✅ Label printed successfully (1 copy)")
                                    else:
                                        self.current_session['labels_printed'] = False
                                        print("⚠️ Failed to print label")

                                    # Gọi webhook 
                                    try:
                                        import os
                                        import threading
                                        from datetime import datetime
                                        import requests

                                        webhook_url = os.environ.get('WEBHOOK_URL')  
                                        if webhook_url:
                                            # customer_data here is a dict
                                            payload = {
                                                'name': customer_data.get('name', ''),
                                                'company': customer_data.get('company', ''),
                                                'phone': customer_data.get('phone', ''),
                                                'email': customer_data.get('email', ''),
                                                'checkin_code': qr_data,
                                            }

                                            def _post_webhook():
                                                try:
                                                    print(f"🔗 Posting webhook to {webhook_url} with payload: {payload}")
                                                    response = requests.post(webhook_url, json=payload, timeout=5)
                                                    print(f"🔗 Webhook response: {response.status_code} {response.text[:200] if hasattr(response, 'text') else ''}")
                                                except Exception as e:
                                                    print(f"⚠️ Webhook post failed: {e}")

                                            threading.Thread(target=_post_webhook, daemon=True).start()
                                        else:
                                            print("ℹ️ WEBHOOK_URL is not set - skipping webhook post")
                                    except Exception as e:
                                        print(f"⚠️ Webhook dispatch error: {e}")
                                        
                                except ImportError as print_import_error:
                                    print(f"⚠️ Print service not available: {print_import_error}")
                                except Exception as print_error:
                                    print(f"⚠️ Error printing label: {print_error}")
                            else:
                                print("⚠️ Failed to generate QR code")
                        else:
                            print("⚠️ No customer data available for QR generation")
                            
                    except ImportError as qr_import_error:
                        print(f"⚠️ QR service not available: {qr_import_error}")
                    except Exception as qr_error:
                        print(f"⚠️ Error generating QR code: {qr_error}")
                else:
                    print("⚠️ Failed to save card info to database")

            except ImportError as import_error:
                print(f"⚠️ Database module not available: {import_error}")
                print("⚠️ Continuing without database operations")
            except Exception as db_error:
                # Thêm thông tin debug chi tiết
                import traceback
                print(f"⚠️ Database operation failed: {db_error}")
                print(f"⚠️ Error type: {type(db_error).__name__}")
                print(f"⚠️ Full traceback:")
                traceback.print_exc()
                print("⚠️ Card info saved to session only")

            self.save_session_data()  # Lưu session data
            print(f"✅ Card info saved to session")  # Log thành công
            return True  # Trả về True

        except Exception as e:
            print(f"❌ Error saving card info: {e}")  # Log lỗi
            return False  # Trả về False nếu có lỗi

    def add_generated_image(self, image_path, image_title="Generated Image"):
        """Thêm tham chiếu ảnh đã tạo (không còn copy vào thư mục session) - Add generated image reference (no longer copying to session directory)"""
        if not self.current_session:  # Nếu không có session hiện tại
            return False  # Trả về False

        try:
            # Ảnh AI đã được lưu trực tiếp bởi ai_generator.py sử dụng PathManager
            # AI images are already saved directly by ai_generator.py using PathManager
            image_index = len(self.current_session['generated_images']) + 1

            # Thêm vào danh sách ảnh đã tạo - Add to generated images list
            image_info = {
                'path': str(image_path),  # Đường dẫn ảnh (đã organized)
                'title': image_title,  # Tiêu đề ảnh
                'created_at': datetime.now().isoformat(),  # Thời gian tạo
                'filename': Path(image_path).name,  # Tên file
                'image_index': image_index  # Index ảnh
            }

            self.current_session['generated_images'].append(image_info)  # Thêm thông tin ảnh vào session
            self.save_session_data()  # Lưu session data

            # Cập nhật AI image path vào database nếu có checkin_id
            self._update_checkin_ai_image(str(image_path))

            print(f"✅ Generated image reference added: {image_path}")  # Log thêm tham chiếu ảnh
            return str(image_path)  # Trả về đường dẫn ảnh

        except Exception as e:
            print(f"❌ Error adding generated image: {e}")  # Log lỗi thêm ảnh
            return None  # Trả về None nếu có lỗi

    def _update_checkin_ai_image(self, ai_image_path):
        """Cập nhật đường dẫn ảnh AI vào TBL_CHECKIN - Update AI image path to TBL_CHECKIN"""
        try:
            checkin_id = self.current_session.get('db_checkin_id')
            if not checkin_id:
                print("⚠️ No checkin_id found in session, cannot update AI image path")
                return False

            from database.db_service import db_service

            # Cập nhật AI image path vào checkin record
            updated_checkin = db_service.checkin_crud.update_images(
                checkin_id=checkin_id,
                ai_img=ai_image_path
            )

            if updated_checkin:
                print(f"✅ Updated AI image path in TBL_CHECKIN (ID: {checkin_id}): {ai_image_path}")
                return True
            else:
                print(f"❌ Failed to update AI image path in TBL_CHECKIN (ID: {checkin_id})")
                return False

        except Exception as e:
            print(f"❌ Error updating checkin AI image: {e}")
            return False

    def save_generated_images(self, generated_images_list):
        """Lưu danh sách ảnh đã tạo vào session - Save list of generated images to session"""
        if not self.current_session:  # Nếu không có session hiện tại
            return False  # Trả về False

        try:
            # Xóa danh sách ảnh cũ và thêm danh sách mới - Clear old images and add new list
            self.current_session['generated_images'] = []

            for i, image_path in enumerate(generated_images_list, 1):
                # Thêm thông tin ảnh vào session - Add image info to session
                image_info = {
                    'path': str(image_path),  # Đường dẫn ảnh
                    'title': f'Generated Image {i}',  # Tiêu đề ảnh
                    'created_at': datetime.now().isoformat(),  # Thời gian tạo
                    'filename': Path(image_path).name,  # Tên file
                    'image_index': i  # Index ảnh
                }
                self.current_session['generated_images'].append(image_info)

            self.save_session_data()  # Lưu session data
            print(f"✅ Saved {len(generated_images_list)} generated images to session")  # Log thành công
            
            # Tự động in ảnh AI sau khi lưu thành công
            try:
                from services.print_service import print_ai_image
                image_paths = [img_info['path'] for img_info in self.current_session['generated_images']]
                print("🖨️ Starting automatic AI image printing...")
                
                # In từng ảnh một
                success_count = 0
                for image_path in image_paths:
                    print(f"🖨️ Printing image: {os.path.basename(image_path)}")
                    if print_ai_image(image_path, copies=1):
                        success_count += 1
                        print(f"✅ Successfully printed: {os.path.basename(image_path)}")
                    else:
                        print(f"❌ Failed to print: {os.path.basename(image_path)}")
                
                if success_count == len(image_paths):
                    self.current_session['ai_images_printed'] = True
                    print("✅ All AI images printed successfully")
                else:
                    self.current_session['ai_images_printed'] = False
                    print(f"⚠️ Only {success_count}/{len(image_paths)} AI images printed successfully")
                    
            except ImportError as print_import_error:
                print(f"⚠️ Print service not available: {print_import_error}")
            except Exception as print_error:
                print(f"⚠️ Error printing AI images: {print_error}")
            
            return True  # Trả về True

        except Exception as e:
            print(f"❌ Error saving generated images: {e}")  # Log lỗi
            return False  # Trả về False nếu có lỗi

    def get_session_status(self):
        """Lấy trạng thái session hiện tại - Get current session status"""
        if not self.current_session:  # Nếu không có session hiện tại
            return {
                'status': 'no_session',  # Trạng thái không có session
                'card_captured': False,  # Chưa chụp card
                'face_captured': False  # Chưa chụp face
            }

        # Trả về thông tin session hiện tại - Return current session info
        return {
            'status': 'active',  # Trạng thái hoạt động
            'session_id': self.current_session['session_id'],  # ID session
            'card_captured': self.current_session['card_captured'],  # Đã chụp card
            'face_captured': self.current_session['face_captured'],  # Đã chụp face
            'card_info': self.current_session.get('card_info'),  # Thông tin card
            'card_image': self.current_session.get('card_image'),  # Đường dẫn ảnh card
            'face_image': self.current_session.get('face_image'),  # Đường dẫn ảnh face
            'generated_images': self.current_session.get('generated_images', [])  # Danh sách ảnh đã tạo
        }



    def cleanup_old_sessions(self, days_old=7):
        """
        Dọn dẹp các session cũ hơn số ngày chỉ định - Clean up sessions older than specified days

        Vì session hiện tại chỉ tồn tại trong bộ nhớ, hàm này sẽ dọn dẹp các file output cũ
        Since current sessions only exist in memory, this function will clean up old output files
        """
        try:
            # Dọn dẹp file output cũ thay vì session files - Clean up old output files instead of session files
            from utils.helpers import cleanup_old_files

            # Dọn dẹp file trong thư mục outputs - Clean up files in outputs directory
            cleaned_count = cleanup_old_files('outputs', days_old=days_old)

            if cleaned_count > 0:  # Nếu có file được dọn dẹp
                print(f"🧹 Cleaned up {cleaned_count} old output files")  # Log số file đã dọn dẹp

            return cleaned_count  # Trả về số file đã dọn dẹp

        except Exception as e:
            print(f"❌ Error cleaning up old files: {e}")  # Log lỗi dọn dẹp file
            return 0  # Trả về 0 nếu có lỗi

    def export_ai_images_to_pdf(self, copies: int = 1) -> bool:
        """
        Export tất cả ảnh AI từ session sang PDF và in
        Export all AI images from session to PDF and print
        
        Args:
            copies: Số bản in cho mỗi ảnh
            
        Returns:
            bool: True nếu thành công, False nếu thất bại
        """
        if not self.current_session:
            print("❌ No active session found")
            return False
            
        generated_images = self.current_session.get('generated_images', [])
        if not generated_images:
            print("❌ No AI images found in current session")
            return False
            
        try:
            from services.print_service import print_service
            
            # Lấy danh sách đường dẫn ảnh
            image_paths = [img_info['path'] for img_info in generated_images]
            
            print(f"🖨️ Exporting {len(image_paths)} AI images to PDF and printing...")
            print(f"   Copies per image: {copies}")
            
            # Sử dụng function create_ai_image_pdf đã có trong print_service
            pdf_paths = []
            for i, image_path in enumerate(image_paths):
                print(f"📄 Creating PDF for image {i+1}: {Path(image_path).name}")
                pdf_path = print_service.create_ai_image_pdf(image_path, copies)
                
                if pdf_path:
                    pdf_paths.append(pdf_path)
                    print(f"✅ PDF created: {Path(pdf_path).name}")
                else:
                    print(f"❌ Failed to create PDF for: {Path(image_path).name}")
                    return False
            
            # In tất cả PDF đã tạo
            success_count = 0
            for pdf_path in pdf_paths:
                print(f"🖨️ Printing PDF: {Path(pdf_path).name}")
                success = print_service.print_pdf(pdf_path, copies)
                
                if success:
                    success_count += 1
                    print(f"✅ Successfully printed PDF: {Path(pdf_path).name}")
                else:
                    print(f"❌ Failed to print PDF: {Path(pdf_path).name}")
            
            if success_count == len(pdf_paths):
                self.current_session['ai_images_exported'] = True
                self.current_session['ai_images_export_time'] = datetime.now().isoformat()
                self.save_session_data()
                print(f"✅ Successfully exported and printed {len(image_paths)} AI images")
                return True
            else:
                self.current_session['ai_images_exported'] = False
                self.save_session_data()
                print(f"❌ Only {success_count}/{len(pdf_paths)} PDFs printed successfully")
                return False
                
        except ImportError as import_error:
            print(f"⚠️ Print service not available: {import_error}")
            return False
        except Exception as e:
            print(f"❌ Error exporting AI images: {e}")
            return False

    def get_ai_images_info(self) -> List[Dict]:
        """
        Lấy thông tin chi tiết về các ảnh AI trong session
        Get detailed information about AI images in session
        
        Returns:
            List[Dict]: Danh sách thông tin ảnh AI
        """
        if not self.current_session:
            return []
            
        generated_images = self.current_session.get('generated_images', [])
        
        # Thêm thông tin bổ sung cho mỗi ảnh
        enhanced_images = []
        for img_info in generated_images:
            enhanced_info = img_info.copy()
            
            # Kiểm tra file có tồn tại không
            image_path = img_info.get('path')
            if image_path and os.path.exists(image_path):
                enhanced_info['exists'] = True
                enhanced_info['file_size'] = os.path.getsize(image_path)
                enhanced_info['file_size_mb'] = round(enhanced_info['file_size'] / (1024 * 1024), 2)
            else:
                enhanced_info['exists'] = False
                enhanced_info['file_size'] = 0
                enhanced_info['file_size_mb'] = 0
                
            enhanced_images.append(enhanced_info)
            
        return enhanced_images