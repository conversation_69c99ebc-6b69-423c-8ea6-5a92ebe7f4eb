from database.connection import db

class Customer(db.Model):
    __tablename__ = 'TBL_CUSTOMER'
    
    customer_id = db.<PERSON>umn(db.Integer, primary_key=True, autoincrement=True)
    company_name = db.Column(db.Text, nullable=True, comment='Tên công ty')
    customer_addr = db.Column(db.Text, nullable=True, comment='Địa chỉ')
    customer_name = db.Column(db.Text, nullable=True, comment='Họ tên đầy đủ')
    customer_title = db.Column(db.Text, nullable=True, comment='Chức vụ')
    customer_email = db.Column(db.Text, nullable=True, comment='Email')
    customer_tel = db.Column(db.Text, nullable=True, comment='Số điện thoại')
    customer_web = db.Column(db.Text, nullable=True, comment='Website')
    customer_infor = db.Column(db.Text, nullable=True, comment='<PERSON><PERSON><PERSON> bộ các thông tin khác')
    
    def to_dict(self):
        return {
            'customer_id': self.customer_id,
            'company_name': self.company_name,
            'customer_addr': self.customer_addr,
            'customer_name': self.customer_name,
            'customer_title': self.customer_title,
            'customer_email': self.customer_email,
            'customer_tel': self.customer_tel,
            'customer_web': self.customer_web,
            'customer_infor': self.customer_infor
        }
    
    def __repr__(self):
        return f'<Customer {self.customer_name} - {self.company_name}>'
