"""
Search Controller - Controller x<PERSON> lý tìm kiếm và hiển thị dữ liệu database
"""

from flask import Blueprint, render_template, request, jsonify
from database.models.customer import Customer
from database.models.checkin import Checkin
from database.connection import db
from sqlalchemy import or_, and_, func
from datetime import datetime
import math

class SearchController:
    """Controller xử lý tìm kiếm và hiển thị dữ liệu từ database"""
    
    def __init__(self):
        self.blueprint = Blueprint('search', __name__, url_prefix='/search')
        self._register_routes()
    
    def _register_routes(self):
        """<PERSON><PERSON><PERSON> ký các routes cho search controller"""
        self.blueprint.add_url_rule('/', 'index', self.index, methods=['GET'])
        self.blueprint.add_url_rule('/customers', 'search_customers', self.search_customers, methods=['GET'])
        self.blueprint.add_url_rule('/checkins', 'search_checkins', self.search_checkins, methods=['GET'])
        self.blueprint.add_url_rule('/customer_info', 'search_customer_info', self.search_customer_info, methods=['GET'])
    
    def index(self):
        """Trang chính hiển thị giao diện tìm kiếm database"""
        try:
            return render_template('search_database.html')
        except Exception as e:
            print(f"❌ Search index error: {e}")
            return f"Error loading search page: {str(e)}", 500
    
    def search_customers(self):
        """API tìm kiếm dữ liệu từ bảng TBL_CUSTOMER với phân trang"""
        try:
            # Lấy parameters từ request
            page = request.args.get('page', 1, type=int)
            per_page = request.args.get('per_page', 20, type=int)
            company = request.args.get('company', '', type=str).strip()
            name = request.args.get('name', '', type=str).strip()
            phone = request.args.get('phone', '', type=str).strip()

            # Giới hạn per_page tối đa 50 để tránh quá tải
            per_page = min(per_page, 50)

            # Tạo query cơ bản
            query = Customer.query

            # Thêm điều kiện tìm kiếm theo từng trường
            search_conditions = []

            if company:
                search_conditions.append(Customer.company_name.ilike(f'%{company}%'))

            if name:
                search_conditions.append(Customer.customer_name.ilike(f'%{name}%'))

            if phone:
                search_conditions.append(Customer.customer_tel.ilike(f'%{phone}%'))

            # Áp dụng các điều kiện tìm kiếm
            if search_conditions:
                query = query.filter(and_(*search_conditions))
            
            # Sắp xếp theo customer_id giảm dần (mới nhất trước)
            query = query.order_by(Customer.customer_id.desc())
            
            # Thực hiện phân trang
            pagination = query.paginate(
                page=page, 
                per_page=per_page, 
                error_out=False
            )
            
            # Chuyển đổi dữ liệu thành dictionary
            customers_data = []
            for customer in pagination.items:
                customers_data.append({
                    'customer_id': customer.customer_id,
                    'customer_name': customer.customer_name or '',
                    'company_name': customer.company_name or '',
                    'customer_title': customer.customer_title or '',
                    'customer_email': customer.customer_email or '',
                    'customer_tel': customer.customer_tel or '',
                    'customer_addr': customer.customer_addr or '',
                    'customer_web': customer.customer_web or '',
                    'customer_infor': customer.customer_infor or ''
                })
            
            # Tính toán thông tin phân trang
            total_pages = pagination.pages
            has_prev = pagination.has_prev
            has_next = pagination.has_next
            prev_num = pagination.prev_num if has_prev else None
            next_num = pagination.next_num if has_next else None
            
            return jsonify({
                'success': True,
                'data': customers_data,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': pagination.total,
                    'total_pages': total_pages,
                    'has_prev': has_prev,
                    'has_next': has_next,
                    'prev_num': prev_num,
                    'next_num': next_num
                },
                'search_params': {
                    'company': company,
                    'name': name,
                    'phone': phone
                }
            })
            
        except Exception as e:
            print(f"❌ Search customers error: {e}")
            return jsonify({
                'success': False,
                'error': str(e),
                'data': [],
                'pagination': {
                    'page': 1,
                    'per_page': 20,
                    'total': 0,
                    'total_pages': 0,
                    'has_prev': False,
                    'has_next': False,
                    'prev_num': None,
                    'next_num': None
                }
            }), 500
    
    def search_checkins(self):
        """API tìm kiếm dữ liệu từ bảng TBL_CHECKIN với phân trang"""
        try:
            # Lấy parameters từ request
            page = request.args.get('page', 1, type=int)
            per_page = request.args.get('per_page', 20, type=int)
            company = request.args.get('company', '', type=str).strip()
            name = request.args.get('name', '', type=str).strip()
            phone = request.args.get('phone', '', type=str).strip()
            date_from = request.args.get('date_from', '', type=str).strip()
            date_to = request.args.get('date_to', '', type=str).strip()

            # Giới hạn per_page tối đa 50 để tránh quá tải
            per_page = min(per_page, 50)

            # Tạo query với join để lấy thông tin customer
            query = db.session.query(Checkin, Customer).outerjoin(
                Customer, Checkin.customer_id == Customer.customer_id
            )

            # Thêm điều kiện tìm kiếm theo từng trường
            search_conditions = []

            if company:
                search_conditions.append(Customer.company_name.ilike(f'%{company}%'))

            if name:
                search_conditions.append(Customer.customer_name.ilike(f'%{name}%'))

            if phone:
                search_conditions.append(Customer.customer_tel.ilike(f'%{phone}%'))

            # Thêm điều kiện tìm kiếm theo thời gian
            if date_from:
                try:
                    from datetime import datetime
                    date_from_obj = datetime.fromisoformat(date_from.replace('T', ' '))
                    search_conditions.append(Checkin.checkin_time >= date_from_obj)
                except ValueError:
                    pass  # Bỏ qua nếu format ngày không đúng

            if date_to:
                try:
                    from datetime import datetime
                    date_to_obj = datetime.fromisoformat(date_to.replace('T', ' '))
                    search_conditions.append(Checkin.checkin_time <= date_to_obj)
                except ValueError:
                    pass  # Bỏ qua nếu format ngày không đúng

            # Áp dụng các điều kiện tìm kiếm
            if search_conditions:
                query = query.filter(and_(*search_conditions))
            
            # Sắp xếp theo checkin_time giảm dần (mới nhất trước)
            query = query.order_by(Checkin.checkin_time.desc())
            
            # Thực hiện phân trang
            pagination = query.paginate(
                page=page, 
                per_page=per_page, 
                error_out=False
            )
            
            # Chuyển đổi dữ liệu thành dictionary
            checkins_data = []
            for checkin, customer in pagination.items:
                checkin_time_str = ''
                if checkin.checkin_time:
                    checkin_time_str = checkin.checkin_time.strftime('%Y-%m-%d %H:%M:%S')

                # Process image paths to ensure they're web-accessible
                def process_image_path(img_path):
                    if not img_path:
                        return ''

                    # Convert to string and normalize path separators
                    web_path = str(img_path).replace('\\', '/')

                    # Handle different path formats
                    if web_path.startswith('static/'):
                        # Already correct format
                        return web_path
                    elif web_path.startswith('/static/'):
                        # Remove leading slash
                        return web_path[1:]
                    elif 'static/' in web_path:
                        # Extract from static/ onwards
                        static_index = web_path.find('static/')
                        return web_path[static_index:]
                    elif web_path.startswith('outputs/'):
                        # AI generated images in outputs folder
                        return web_path
                    elif 'outputs/' in web_path:
                        # Extract from outputs/ onwards
                        outputs_index = web_path.find('outputs/')
                        return web_path[outputs_index:]
                    else:
                        # Default: assume it's relative to static
                        return f'static/{web_path}' if web_path else ''

                # Process image paths
                card_img_processed = process_image_path(checkin.card_img)
                face_img_processed = process_image_path(checkin.face_img)
                ai_img_processed = process_image_path(checkin.ai_img)

                # Debug logging
                print(f"🖼️ Image path processing for checkin {checkin.checkin_id}:")
                print(f"   Card: '{checkin.card_img}' → '{card_img_processed}'")
                print(f"   Face: '{checkin.face_img}' → '{face_img_processed}'")
                print(f"   AI: '{checkin.ai_img}' → '{ai_img_processed}'")

                checkins_data.append({
                    'checkin_id': checkin.checkin_id,
                    'customer_id': checkin.customer_id or '',
                    'checkin_time': checkin_time_str,
                    'card_img': card_img_processed,
                    'face_img': face_img_processed,
                    'ai_img': ai_img_processed
                })
            
            # Tính toán thông tin phân trang
            total_pages = pagination.pages
            has_prev = pagination.has_prev
            has_next = pagination.has_next
            prev_num = pagination.prev_num if has_prev else None
            next_num = pagination.next_num if has_next else None
            
            return jsonify({
                'success': True,
                'data': checkins_data,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': pagination.total,
                    'total_pages': total_pages,
                    'has_prev': has_prev,
                    'has_next': has_next,
                    'prev_num': prev_num,
                    'next_num': next_num
                },
                'search_params': {
                    'company': company,
                    'name': name,
                    'phone': phone,
                    'date_from': date_from,
                    'date_to': date_to
                }
            })
            
        except Exception as e:
            print(f"❌ Search checkins error: {e}")
            return jsonify({
                'success': False,
                'error': str(e),
                'data': [],
                'pagination': {
                    'page': 1,
                    'per_page': 20,
                    'total': 0,
                    'total_pages': 0,
                    'has_prev': False,
                    'has_next': False,
                    'prev_num': None,
                    'next_num': None
                }
            }), 500

    def search_customer_info(self):
        """API tìm kiếm thông tin khách hàng gộp từ cả 2 bảng TBL_CUSTOMER và TBL_CHECKIN"""
        try:
            # Lấy parameters từ request
            page = request.args.get('page', 1, type=int)
            per_page = request.args.get('per_page', 20, type=int)
            company = request.args.get('company', '', type=str).strip()
            name = request.args.get('name', '', type=str).strip()
            phone = request.args.get('phone', '', type=str).strip()
            date_from = request.args.get('date_from', '', type=str).strip()
            date_to = request.args.get('date_to', '', type=str).strip()
            sort_order = request.args.get('sort_order', 'desc', type=str).strip()  # 'asc' hoặc 'desc'

            # Giới hạn per_page tối đa 50 để tránh quá tải
            per_page = min(per_page, 50)

            # Tạo query với left join để lấy thông tin từ cả 2 bảng
            # Sử dụng left join để luôn hiển thị customer dù có checkin hay không
            query = db.session.query(Customer).outerjoin(
                Checkin, Customer.customer_id == Checkin.customer_id
            )

            # Thêm điều kiện tìm kiếm theo từng trường
            search_conditions = []

            if company:
                search_conditions.append(Customer.company_name.ilike(f'%{company}%'))

            if name:
                search_conditions.append(Customer.customer_name.ilike(f'%{name}%'))

            if phone:
                search_conditions.append(Customer.customer_tel.ilike(f'%{phone}%'))

            # Thêm điều kiện tìm kiếm theo thời gian checkin
            if date_from:
                try:
                    from datetime import datetime
                    date_from_obj = datetime.fromisoformat(date_from.replace('T', ' '))
                    search_conditions.append(Checkin.checkin_time >= date_from_obj)
                except ValueError:
                    pass  # Bỏ qua nếu format ngày không đúng

            if date_to:
                try:
                    from datetime import datetime
                    date_to_obj = datetime.fromisoformat(date_to.replace('T', ' '))
                    search_conditions.append(Checkin.checkin_time <= date_to_obj)
                except ValueError:
                    pass  # Bỏ qua nếu format ngày không đúng

            # Áp dụng các điều kiện tìm kiếm
            if search_conditions:
                query = query.filter(and_(*search_conditions))

            # Sắp xếp theo customer_id (có thể đảo ngược)
            if sort_order == 'asc':
                query = query.order_by(Customer.customer_id.asc())
            else:
                query = query.order_by(Customer.customer_id.desc())

            # Thực hiện phân trang
            pagination = query.paginate(
                page=page,
                per_page=per_page,
                error_out=False
            )

            # Chuyển đổi dữ liệu thành dictionary gộp
            customer_info_data = []
            stt = (page - 1) * per_page + 1  # Số thứ tự bắt đầu từ 1

            for customer in pagination.items:
                # Lấy checkin mới nhất của customer này
                checkin = db.session.query(Checkin).filter(
                    Checkin.customer_id == customer.customer_id
                ).order_by(Checkin.checkin_time.desc()).first()
                # Xử lý thời gian checkin
                checkin_time_str = ''
                if checkin and checkin.checkin_time:
                    checkin_time_str = checkin.checkin_time.strftime('%Y-%m-%d %H:%M:%S')

                # Xử lý đường dẫn ảnh
                def process_image_path(img_path):
                    if not img_path:
                        return ''

                    # Convert to string and normalize path separators
                    web_path = str(img_path).replace('\\', '/')

                    # Handle different path formats
                    if web_path.startswith('static/'):
                        return web_path
                    elif web_path.startswith('/static/'):
                        return web_path[1:]
                    elif 'static/' in web_path:
                        static_index = web_path.find('static/')
                        return web_path[static_index:]
                    elif web_path.startswith('outputs/'):
                        return web_path
                    elif 'outputs/' in web_path:
                        outputs_index = web_path.find('outputs/')
                        return web_path[outputs_index:]
                    else:
                        return f'static/{web_path}' if web_path else ''

                # Xử lý ảnh từ checkin
                card_img_processed = process_image_path(checkin.card_img) if checkin else ''
                face_img_processed = process_image_path(checkin.face_img) if checkin else ''
                ai_img_processed = process_image_path(checkin.ai_img) if checkin else ''

                customer_info_data.append({
                    'stt': stt,  # Số thứ tự thay vì customer_id
                    'customer_name': customer.customer_name or '',
                    'company_name': customer.company_name or '',
                    'customer_title': customer.customer_title or '',
                    'customer_email': customer.customer_email or '',
                    'customer_tel': customer.customer_tel or '',
                    'customer_addr': customer.customer_addr or '',
                    'customer_web': customer.customer_web or '',
                    'checkin_time': checkin_time_str,
                    'card_img': card_img_processed,
                    'face_img': face_img_processed,
                    'ai_img': ai_img_processed
                })
                stt += 1

            # Tính toán thông tin phân trang
            total_pages = pagination.pages
            has_prev = pagination.has_prev
            has_next = pagination.has_next
            prev_num = pagination.prev_num if has_prev else None
            next_num = pagination.next_num if has_next else None

            return jsonify({
                'success': True,
                'data': customer_info_data,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': pagination.total,
                    'total_pages': total_pages,
                    'has_prev': has_prev,
                    'has_next': has_next,
                    'prev_num': prev_num,
                    'next_num': next_num
                },
                'search_params': {
                    'company': company,
                    'name': name,
                    'phone': phone,
                    'date_from': date_from,
                    'date_to': date_to,
                    'sort_order': sort_order
                }
            })

        except Exception as e:
            print(f"❌ Search customer info error: {e}")
            return jsonify({
                'success': False,
                'error': str(e),
                'data': [],
                'pagination': {
                    'page': 1,
                    'per_page': 20,
                    'total': 0,
                    'total_pages': 0,
                    'has_prev': False,
                    'has_next': False,
                    'prev_num': None,
                    'next_num': None
                }
            }), 500

def get_search_blueprint():
    """Trả về blueprint cho search controller"""
    controller = SearchController()
    return controller.blueprint
