from typing import List, Optional, Any
from database.connection import db
from .exceptions import RecordNotFound, ValidationError

class BaseCRUD:

    def __init__(self, model):
        self.model = model
    
    def create(self, **kwargs) -> Any:
        import time
        max_retries = 3
        retry_delay = 1
        
        for attempt in range(max_retries):
            try:
                obj = self.model(**kwargs)
                db.session.add(obj)
                db.session.commit()
                db.session.refresh(obj)
                return obj
            except Exception as e:
                db.session.rollback()
                if "database is locked" in str(e) and attempt < max_retries - 1:
                    print(f"⚠️ Database locked, retrying in {retry_delay}s (attempt {attempt + 1}/{max_retries})")
                    time.sleep(retry_delay)
                    retry_delay *= 2  # Exponential backoff
                    continue
                else:
                    raise ValidationError(f"Lỗi tạo record: {str(e)}")

    def get_by_id(self, record_id: int) -> Optional[Any]:
        return self.model.query.get(record_id)

    def get_by_id_or_404(self, record_id: int) -> Any:
        obj = self.get_by_id(record_id)
        if not obj:
            raise RecordNotFound(f"Không tìm thấy {self.model.__name__} với ID {record_id}")
        return obj
    
    def get_all(self, limit: Optional[int] = None, offset: int = 0) -> List[Any]:
        query = self.model.query.offset(offset)
        if limit:
            query = query.limit(limit)
        return query.all()

    def update(self, record_id: int, **kwargs) -> Any:
        import time
        max_retries = 3
        retry_delay = 1
        
        for attempt in range(max_retries):
            try:
                obj = self.get_by_id_or_404(record_id)
                for key, value in kwargs.items():
                    if hasattr(obj, key):
                        setattr(obj, key, value)
                db.session.commit()
                db.session.refresh(obj)
                return obj
            except Exception as e:
                db.session.rollback()
                if "database is locked" in str(e) and attempt < max_retries - 1:
                    print(f"⚠️ Database locked, retrying in {retry_delay}s (attempt {attempt + 1}/{max_retries})")
                    time.sleep(retry_delay)
                    retry_delay *= 2  # Exponential backoff
                    continue
                else:
                    raise ValidationError(f"Lỗi cập nhật record: {str(e)}")

    def delete(self, record_id: int) -> bool:
        try:
            obj = self.get_by_id_or_404(record_id)
            db.session.delete(obj)
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            raise ValidationError(f"Lỗi xóa record: {str(e)}")

    def count(self) -> int:
        return self.model.query.count()

    def exists(self, record_id: int) -> bool:
        return self.model.query.filter_by(id=record_id).first() is not None
