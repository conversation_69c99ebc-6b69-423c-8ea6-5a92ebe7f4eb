#!/usr/bin/env python3
"""
Camera Swap Tool - Công cụ hoán đổi camera
Tool để hoán đổi chức năng giữa Camera 1 và Camera 2

Logic thực tế (với 2 USB cameras):
- Camera 0: Laptop camera (không dùng)
- Camera 1: USB camera thứ 1 (Card hoặc Face)
- Camera 2: USB camera thứ 2 (Face hoặc Card)
- Swap: <PERSON><PERSON><PERSON> ng<PERSON><PERSON> chức năng Camera 1 ↔ Camera 2
"""

import json
import os
import cv2
import time

def detect_cameras():
    """Phát hiện camera 0, 1, 2 thôi"""
    print("🔍 Đang phát hiện camera 0, 1, 2...")
    cameras = {}
    
    for i in range(3):  # Chỉ test camera 0, 1, 2
        try:
            cap = cv2.VideoCapture(i, cv2.CAP_DSHOW)
            time.sleep(0.3)
            
            if cap.isOpened():
                ret, frame = cap.read()
                if ret and frame is not None:
                    width = cap.get(cv2.CAP_PROP_FRAME_WIDTH)
                    height = cap.get(cv2.CAP_PROP_FRAME_HEIGHT)
                    cameras[i] = f"{int(width)}x{int(height)}"
                    
                    if i == 0:
                        print(f"   ✅ Camera {i}: Laptop camera (không dùng) ({cameras[i]})")
                    elif i == 1:
                        print(f"   ✅ Camera {i}: USB camera thứ 1 ({cameras[i]})")
                    else:
                        print(f"   ✅ Camera {i}: USB camera thứ 2 ({cameras[i]})")
                else:
                    print(f"   ❌ Camera {i}: Không đọc được frame")
            else:
                print(f"   ❌ Camera {i}: Không mở được")
                
            cap.release()
        except Exception as e:
            print(f"   ❌ Camera {i}: Lỗi {e}")
    
    return cameras

def get_current_mapping():
    """Lấy mapping camera hiện tại"""
    if os.path.exists('camera_mapping.json'):
        try:
            with open('camera_mapping.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"⚠️ Lỗi đọc file mapping: {e}")
    return None

def show_current_status():
    """Hiển thị trạng thái camera hiện tại"""
    print("\n" + "="*60)
    print("📋 TRẠNG THÁI CAMERA HIỆN TẠI")
    print("="*60)
    
    # Hiển thị camera có sẵn
    cameras = detect_cameras()
    
    # Kiểm tra camera USB
    usb_cameras = [cam for cam in cameras.keys() if cam in [1, 2]]
    print(f"\n📁 Tổng kết:")
    print(f"   💻 Camera 0 (Laptop): {'Có' if 0 in cameras else 'Không có'}")
    print(f"   📱 Camera USB: {usb_cameras} (Cần ít nhất Camera 1, 2)")
    
    if len(usb_cameras) < 2:
        print(f"\n⚠️ Cảnh báo: Cần cả Camera 1 và Camera 2 để hoán đổi!")
        return None
    
    # Hiển thị mapping hiện tại
    mapping = get_current_mapping()
    if mapping:
        print(f"\n🎯 MAPPING HIỆN TẠI:")
        business_cam = mapping.get('business_card_camera')
        face_cam = mapping.get('face_camera')
        
        # Hiển thị với mô tả loại camera
        business_type = "USB camera thứ 2" if business_cam == 2 else "USB camera thứ 1" if business_cam == 1 else "Laptop camera" if business_cam == 0 else "Unknown"
        face_type = "USB camera thứ 2" if face_cam == 2 else "USB camera thứ 1" if face_cam == 1 else "Laptop camera" if face_cam == 0 else "Unknown"
        
        print(f"   📄 Business Card (Chụp thẻ): Camera {business_cam} ({business_type})")
        print(f"   👤 Face (Chụp mặt): Camera {face_cam} ({face_type})")
        
        # Kiểm tra xem mapping có hợp lý không
        if business_cam in [1, 2] and face_cam in [1, 2] and business_cam != face_cam:
            print(f"   ✅ Mapping hợp lý")
        else:
            print(f"   ⚠️ Mapping không hợp lý")
            
    else:
        print(f"\n⚠️ Chưa có file mapping camera (camera_mapping.json)")
        
        # Tạo mapping mặc định nếu có đủ camera
        if 1 in cameras and 2 in cameras:
            default_mapping = {
                'business_card_camera': 1,  # Camera 1 làm Card
                'face_camera': 2            # Camera 2 làm Face
            }
            print(f"\n💡 Tạo mapping mặc định:")
            print(f"   📄 Business Card: Camera 1")
            print(f"   👤 Face: Camera 2")
            
            print(f"\n❓ Tạo mapping mặc định này? (y/n): ", end="")
            if input().strip().lower() in ['y', 'yes', 'có']:
                with open('camera_mapping.json', 'w', encoding='utf-8') as f:
                    json.dump(default_mapping, f, indent=2, ensure_ascii=False)
                print(f"✅ Đã tạo mapping mặc định!")
                return default_mapping
    
    return mapping

def swap_cameras():
    """Hoán đổi vị trí Camera 1 ↔ Camera 2"""
    print("\n" + "="*60)
    print("🔄 HOÁN ĐỔI CAMERA 1 ↔ CAMERA 2")
    print("="*60)
    
    # Kiểm tra camera có sẵn
    cameras = detect_cameras()
    if 1 not in cameras or 2 not in cameras:
        print("❌ Không tìm thấy đủ Camera 1 và Camera 2!")
        print("   Cần cả Camera 1 và Camera 2 để hoán đổi.")
        return False
    
    current_mapping = get_current_mapping()
    
    if not current_mapping:
        print("❌ Không tìm thấy file mapping camera!")
        print("   Vui lòng chạy chức năng 1 để tạo mapping trước.")
        return False
    
    # Lấy mapping hiện tại
    old_business = current_mapping.get('business_card_camera')
    old_face = current_mapping.get('face_camera')
    
    print(f"\n📋 MAPPING HIỆN TẠI:")
    # Hiển thị với mô tả loại camera
    old_business_type = "USB camera thứ 2" if old_business == 2 else "USB camera thứ 1" if old_business == 1 else "Laptop camera" if old_business == 0 else "Unknown"
    old_face_type = "USB camera thứ 2" if old_face == 2 else "USB camera thứ 1" if old_face == 1 else "Laptop camera" if old_face == 0 else "Unknown"
    
    print(f"   📄 Business Card: Camera {old_business} ({old_business_type})")
    print(f"   👤 Face: Camera {old_face} ({old_face_type})")
    
    # Kiểm tra mapping có hợp lý không
    if old_business not in [1, 2] or old_face not in [1, 2]:
        print(f"\n❌ Mapping hiện tại không hợp lý!")
        print(f"   Business Card và Face phải là Camera 1 hoặc Camera 2")
        return False
    
    if old_business == old_face:
        print(f"\n❌ Mapping hiện tại không hợp lý!")
        print(f"   Business Card và Face không thể cùng một camera")
        return False
    
    # Tạo mapping mới (hoán đổi Camera 1 ↔ Camera 2)
    new_mapping = {
        'business_card_camera': old_face,    # Đảo ngược
        'face_camera': old_business          # Đảo ngược
    }
    
    print(f"\n🔄 SAU KHI HOÁN ĐỔI:")
    # Hiển thị với mô tả loại camera
    new_business_type = "USB camera thứ 2" if new_mapping['business_card_camera'] == 2 else "USB camera thứ 1" if new_mapping['business_card_camera'] == 1 else "Laptop camera" if new_mapping['business_card_camera'] == 0 else "Unknown"
    new_face_type = "USB camera thứ 2" if new_mapping['face_camera'] == 2 else "USB camera thứ 1" if new_mapping['face_camera'] == 1 else "Laptop camera" if new_mapping['face_camera'] == 0 else "Unknown"
    
    print(f"   📄 Business Card: Camera {new_mapping['business_card_camera']} ({new_business_type}) ← (trước: Camera {old_business})")
    print(f"   👤 Face: Camera {new_mapping['face_camera']} ({new_face_type}) ← (trước: Camera {old_face})")
    
    print(f"\n💡 Nói cách khác:")
    # Xác định chức năng cũ và mới của từng camera dựa trên mapping thực tế
    old_business_function = "Card"  # Camera business_card_camera luôn là Card
    old_face_function = "Face"      # Camera face_camera luôn là Face
    new_business_function = "Card"  # Camera business_card_camera mới vẫn là Card
    new_face_function = "Face"      # Camera face_camera mới vẫn là Face
    
    print(f"   Camera {old_business}: {old_business_function} → {new_face_function}")
    print(f"   Camera {old_face}: {old_face_function} → {new_business_function}")
    
    print(f"\n❓ Xác nhận hoán đổi? (y/n): ", end="")
    confirm = input().strip().lower()
    
    if confirm in ['y', 'yes', 'có']:
        try:
            # Backup file cũ
            if os.path.exists('camera_mapping.json'):
                backup_name = 'camera_mapping_backup.json'
                with open('camera_mapping.json', 'r') as f_old:
                    with open(backup_name, 'w') as f_backup:
                        f_backup.write(f_old.read())
                print(f"💾 Đã backup mapping cũ vào {backup_name}")
            
            # Lưu mapping mới với logging chi tiết
            print(f"\n💾 Đang lưu mapping mới...")
            with open('camera_mapping.json', 'w', encoding='utf-8') as f:
                json.dump(new_mapping, f, indent=2, ensure_ascii=False)
            
            # Kiểm tra lại file vừa lưu
            print(f"🔍 Kiểm tra lại file vừa lưu...")
            with open('camera_mapping.json', 'r', encoding='utf-8') as f:
                verify_mapping = json.load(f)
            
            print(f"\n📋 File đã lưu chứa:")
            print(f"   business_card_camera: {verify_mapping.get('business_card_camera')}")
            print(f"   face_camera: {verify_mapping.get('face_camera')}")
            
            # Kiểm tra đường dẫn file
            abs_path = os.path.abspath('camera_mapping.json')
            print(f"   📁 Đường dẫn file: {abs_path}")
            print(f"   📊 Kích thước file: {os.path.getsize('camera_mapping.json')} bytes")
            
            print(f"\n✅ HOÁN ĐỔI THÀNH CÔNG!")
            print(f"📄 Business Card Camera: {new_mapping['business_card_camera']}")
            print(f"👤 Face Camera: {new_mapping['face_camera']}")
            print(f"\n🚀 Bây giờ hãy KHỞI ĐỘNG LẠI ứng dụng app.py để áp dụng thay đổi!")
            print(f"⚠️ Lưu ý: Đảm bảo dừng app.py hoàn toàn trước khi chạy lại!")
            
            return True
            
        except Exception as e:
            print(f"❌ Lỗi khi lưu mapping: {e}")
            import traceback
            traceback.print_exc()
            return False
    else:
        print(f"❌ Đã hủy hoán đổi")
        return False

def test_camera_preview(camera_id):
    """Test một camera với preview"""
    print(f"\n🎥 Test Camera {camera_id}...")
    try:
        cap = cv2.VideoCapture(camera_id, cv2.CAP_DSHOW)
        time.sleep(0.5)
        
        if not cap.isOpened():
            print(f"❌ Camera {camera_id} không thể mở")
            return False
        
        print(f"   📹 Hiển thị preview Camera {camera_id} trong 3 giây...")
        print(f"   💡 Nhấn phím 'q' để thoát sớm")
        
        start_time = time.time()
        frame_count = 0
        
        while time.time() - start_time < 3:
            ret, frame = cap.read()
            if ret and frame is not None:
                frame_count += 1
                
                # Thêm text hiển thị
                text = f"Camera {camera_id} - Frame {frame_count}"
                cv2.putText(frame, text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
                
                # Resize để dễ xem
                height, width = frame.shape[:2]
                if width > 800:
                    scale = 800 / width
                    new_width = int(width * scale)
                    new_height = int(height * scale)
                    frame = cv2.resize(frame, (new_width, new_height))
                
                cv2.imshow(f'Camera {camera_id} Preview', frame)
                
                # Nhấn 'q' để thoát sớm
                if cv2.waitKey(1) & 0xFF == ord('q'):
                    break
        
        cv2.destroyAllWindows()
        cap.release()
        print(f"   ✅ Camera {camera_id} test hoàn thành")
        return True
        
    except Exception as e:
        print(f"   ❌ Lỗi test Camera {camera_id}: {e}")
        return False

def test_all_cameras():
    """Test tất cả camera 0, 1, 2"""
    print("\n" + "="*60)
    print("🧪 TEST TẤT CẢ CAMERA")
    print("="*60)
    
    cameras = detect_cameras()
    
    if not cameras:
        print("❌ Không tìm thấy camera nào!")
        return
    
    for cam_id in [0, 1, 2]:
        if cam_id in cameras:
            test_camera_preview(cam_id)
        else:
            print(f"\n❌ Camera {cam_id}: Không có sẵn")

def main():
    print("🎥 CÔNG CỤ HOÁN ĐỔI CAMERA USB")
    print("="*60)
    print("Logic: Camera 1 ↔ Camera 2 (Card ↔ Face)")
    print("Chỉ làm việc với Camera 0, 1, 2")
    print("⚠️ LưU Ý: Force mapping đã được tắt - camera swap sẽ hoạt động!")
    
    while True:
        print(f"\n🎛️ CHỨC NĂNG:")
        print(f"   1. Xem trạng thái camera hiện tại")
        print(f"   2. HOÁN ĐỔI Camera 1 ↔ Camera 2")
        print(f"   3. Test camera với preview")
        print(f"   4. Thoát")
        
        choice = input(f"\nChọn chức năng (1-4): ").strip()
        
        if choice == '1':
            show_current_status()
            
        elif choice == '2':
            if swap_cameras():
                print(f"\n🎉 Hoán đổi thành công!")
                print(f"⚠️ Nhớ khởi động lại app.py để áp dụng thay đổi!")
            
        elif choice == '3':
            test_all_cameras()
            
        elif choice == '4':
            print(f"\n👋 Tạm biệt!")
            break
            
        else:
            print(f"❌ Lựa chọn không hợp lệ! Vui lòng chọn từ 1-4.")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print(f"\n\n⚠️ Đã dừng bởi người dùng (Ctrl+C)")
    except Exception as e:
        print(f"\n❌ Lỗi không mong đợi: {e}")
        print("Vui lòng thử lại hoặc kiểm tra lại camera.")