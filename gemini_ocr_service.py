#!/usr/bin/env python3
"""
Gemini OCR Service - CHỈ DÙNG GEMINI 2.5 CHO OCR
Dịch vụ OCR sử dụng Gemini 2.5 để đọc thông tin từ name card
"""

import os  # Thư viện hệ điều hành
import json  # Thư viện xử lý JSON
import base64  # Thư viện mã hóa base64 cho ảnh
import requests  # Thư viện gửi HTTP requests
from pathlib import Path  # Thư viện xử lý đường dẫn file
import time  # Thư viện đo thời gian để performance monitoring
from typing import Optional, Tuple

# =====================
# Performance settings - HARD-CODED for production check-in (no env needed)
# Keep old env-based logic commented for reference
# =====================
# [OLD]
# FAST_MODE = os.environ.get('OCR_FAST_MODE', 'true').lower() == 'true'
# FASTEST_MODE = os.environ.get('OCR_FASTEST_MODE', 'false').lower() == 'true'
# [NEW] - SPEED OPTIMIZED: Giảm kích thước ảnh để tăng tốc API
FAST_MODE = True
FASTEST_MODE = True

# [OLD]
# OCR_MAX_SIZE = int(os.environ.get('OCR_MAX_SIZE', (1024 if FASTEST_MODE else (1536 if FAST_MODE else 2048))))
# OCR_MIN_SIZE = int(os.environ.get('OCR_MIN_SIZE', (700 if FASTEST_MODE else (800 if FAST_MODE else 1024))))
# [NEW] - ULTRA SPEED: Giảm kích thước tối đa mà vẫn OCR được
OCR_MAX_SIZE = 640  # Giảm từ 800 -> 640 (tối thiểu cho OCR tốt)
OCR_MIN_SIZE = 480  # Giảm từ 600 -> 480

# [OLD]
# OCR_CONTRAST = float(os.environ.get('OCR_CONTRAST', (1.2 if FASTEST_MODE else (1.3 if FAST_MODE else 1.6))))
# OCR_BRIGHTNESS = float(os.environ.get('OCR_BRIGHTNESS', 1.0))
# OCR_SHARPNESS = float(os.environ.get('OCR_SHARPNESS', (1.1 if FASTEST_MODE else (1.2 if FAST_MODE else 1.6))))
# [NEW] - ULTRA SPEED: Tối thiểu enhancement
OCR_CONTRAST = 1.5  # Giảm từ 1.2 -> 1.1 (tối thiểu)
OCR_BRIGHTNESS = 1.5
OCR_SHARPNESS = 1.5  # Tắt sharpness để tăng tốc

# [OLD]
# OCR_ENABLE_UNSHARP = os.environ.get('OCR_ENABLE_UNSHARP', 'false').lower() == 'true'
# OCR_UNSHARP_RADIUS = float(os.environ.get('OCR_UNSHARP_RADIUS', 1.0))
# OCR_UNSHARP_PERCENT = int(os.environ.get('OCR_UNSHARP_PERCENT', 150))
# OCR_UNSHARP_THRESHOLD = int(os.environ.get('OCR_UNSHARP_THRESHOLD', 3))
# [NEW] - SPEED OPTIMIZED: Tắt unsharp mask để tăng tốc
OCR_ENABLE_UNSHARP = False  # Tắt để tăng tốc
OCR_UNSHARP_RADIUS = 0.8
OCR_UNSHARP_PERCENT = 120
OCR_UNSHARP_THRESHOLD = 3

# [OLD]
# OCR_JPEG_QUALITY = int(os.environ.get('OCR_JPEG_QUALITY', (70 if FASTEST_MODE else (75 if FAST_MODE else 85))))
# OCR_JPEG_SUBSAMPLING = int(os.environ.get('OCR_JPEG_SUBSAMPLING', 2))
# [NEW]
OCR_JPEG_QUALITY = 72
OCR_JPEG_SUBSAMPLING = 2

# [OLD]
# OCR_HTTP_TIMEOUT = int(os.environ.get('OCR_HTTP_TIMEOUT', (10 if FASTEST_MODE else (15 if FAST_MODE else 30))))
# OCR_CACHE_TTL = int(os.environ.get('OCR_CACHE_TTL', 900))
# [NEW] - ULTRA SPEED: Timeout tối thiểu
OCR_HTTP_TIMEOUT = 10  # Giảm từ 8 -> 6 để fail fast hơn
OCR_CACHE_TTL = 600  # Tăng cache 10 phút

class GeminiOCRService:
    """
    Dịch vụ OCR Gemini - Giai đoạn 1 của Two-Stage AI Pipeline
    Gemini OCR Service - Stage 1 of Two-Stage AI Pipeline

    Sử dụng Gemini 2.5 Flash chuyên biệt cho Optical Character Recognition (OCR)
    Uses Gemini 2.5 Flash specifically for Optical Character Recognition (OCR)
    với độ chính xác cao thông qua:
    with enhanced accuracy through:
    - Tiền xử lý ảnh (resize, tăng cường độ tương phản/độ sắc nét, unsharp mask)
    - Image preprocessing (resize, enhance contrast/sharpness, unsharp mask)
    - Thiết lập API xác định (temperature=0.0, topK=1, topP=0.1)
    - Deterministic API settings (temperature=0.0, topK=1, topP=0.1)
    - Prompt OCR nâng cao để tối đa hóa độ chính xác nhận dạng văn bản
    - Enhanced OCR prompts for maximum text recognition accuracy
    """

    def __init__(self):
        """Khởi tạo dịch vụ OCR với connection pooling"""
        self.load_api_key()  # Tải API key
        self.base_url = "https://generativelanguage.googleapis.com/v1beta"  # URL cơ sở của API
        self.model_name = "gemini-2.5-flash"  # Model chuyên biệt cho độ chính xác OCR - Specifically for OCR accuracy

        # OPTIMIZED: Tạo session với connection pooling để tái sử dụng kết nối
        self.session = requests.Session()
        self.session.headers.update({"Content-Type": "application/json"})

        # Simple in-memory cache để tránh OCR lại cùng một ảnh trong thời gian ngắn
        self._cache = {}  # key -> (timestamp, result_dict)
        self._cache_ttl = OCR_CACHE_TTL

        print(f"👁️ Gemini 2.5 Flash OCR Service initialized")
        print(f"   Model: {self.model_name}")
        print(f"   Purpose: Stage 1 - OCR Processing")
        print(f"   Features: Image preprocessing, deterministic settings, enhanced prompts")
        print(f"   Optimization: Connection pooling enabled")

    def reset_state(self):
        """Reset state cho workflow mới - Reset state for new workflow"""
        print("🔄 Resetting OCR Service state for new workflow...")
        # Clear cache để tránh conflict với workflow mới
        self._cache.clear()
        print("✅ OCR Service state reset completed")

    def load_api_key(self):
        """Tải Gemini API key với hỗ trợ rotation - Load Gemini API key with rotation support"""

        # Thử tải từ file .env - Try to load from .env file
        env_file = Path(".env")
        if env_file.exists():  # Nếu file .env tồn tại
            with open(env_file, 'r') as f:  # Mở file để đọc
                for line in f:  # Đọc từng dòng
                    if '=' in line and not line.startswith('#'):  # Nếu dòng chứa biến môi trường
                        key, value = line.strip().split('=', 1)  # Tách key và value
                        os.environ[key] = value  # Đặt biến môi trường

        # Lấy tất cả API key có sẵn từ environment variables - Get all available API keys
        api_keys = [
            os.getenv('GEMINI_API_KEY'),  # API key chính từ .env
            os.getenv('GEMINI_API_KEY_2'),  # API key dự phòng 1 từ .env
            os.getenv('GEMINI_API_KEY_3')  # API key dự phòng 2 từ .env
        ]

        # Lọc bỏ key None/rỗng và key placeholder - Filter out None/empty keys and placeholder keys
        valid_keys = [key for key in api_keys if key and key.strip() and not key.startswith('YOUR_')]  # Chỉ lấy key hợp lệ

        if not valid_keys:  # Nếu không có key hợp lệ nào
            self.gemini_key = ''  # Đặt key chính rỗng
            self.backup_keys = []  # Danh sách key dự phòng rỗng
            print("⚠️ Không tìm thấy Gemini API key hợp lệ nào")  # Log cảnh báo không có key
            return  # Thoát khỏi function

        # Sử dụng key hợp lệ đầu tiên làm chính - Use first valid key as primary
        self.gemini_key = valid_keys[0]  # Gán key đầu tiên làm key chính
        self.backup_keys = valid_keys[1:] if len(valid_keys) > 1 else []  # Các key còn lại làm dự phòng

        print(f"✅ Gemini API key loaded for OCR")  # Log tải key thành công
        print(f"   API keys available: {len(valid_keys)}")  # Log số lượng key có sẵn
        if self.backup_keys:  # Nếu có key dự phòng
            print(f"   Backup keys available: {len(self.backup_keys)}")  # Log số lượng key dự phòng

    def switch_to_backup_key(self):
        """Chuyển sang API key dự phòng khi bị giới hạn tốc độ - Switch to backup API key when rate limit hit"""
        if not self.backup_keys:  # Nếu không có key dự phòng nào
            print("   ❌ Không còn API key dự phòng nào khả dụng cho OCR")  # Log không có key dự phòng
            return False  # Trả về False để báo thất bại

        # Chuyển key hiện tại vào cuối danh sách dự phòng - Move current key to end of backup list
        self.backup_keys.append(self.gemini_key)  # Thêm key hiện tại vào cuối danh sách dự phòng

        # Sử dụng key dự phòng đầu tiên làm key chính mới - Use first backup key as new primary
        self.gemini_key = self.backup_keys.pop(0)  # Lấy key đầu tiên từ danh sách dự phòng

        print(f"   🔄 OCR đã chuyển sang API key dự phòng: {self.gemini_key[:20]}...")  # Log chuyển sang key dự phòng
        print(f"   📊 Còn lại {len(self.backup_keys)} key dự phòng cho OCR")  # Log số key dự phòng còn lại

        return True  # Trả về True để báo chuyển key thành công

    def extract_text_from_card(self, card_path):
        """Trích xuất văn bản từ name card sử dụng Gemini 2.5 - Extract text from business card using Gemini 2.5"""

        # PERFORMANCE MONITORING: Bắt đầu đo thời gian tổng thể
        total_start_time = time.time()
        print(f"📄 Extracting text from: {Path(card_path).name}")  # Log file đang xử lý

        if not self.gemini_key:  # Nếu không có API key
            return self._get_fallback_card_info()  # Trả về thông tin mặc định

        try:
            # CACHE: kiểm tra cache theo path + mtime + size
            cache_key = self._build_cache_key(card_path)
            cached = self._get_cached_result(cache_key)
            if cached is not None:
                total_time = time.time() - total_start_time
                print(f"⚡ OCR cache hit (age<{self._cache_ttl}s) -> total {total_time:.2f}s")
                return cached

            # PERFORMANCE MONITORING: Đo thời gian encoding ảnh
            encoding_start_time = time.time()
            # Mã hóa ảnh name card - Encode card image
            card_data = self._encode_image(card_path)  # Mã hóa ảnh thành base64
            encoding_time = time.time() - encoding_start_time
            print(f"⏱️ Image encoding time: {encoding_time:.2f}s")

            if not card_data:  # Nếu không mã hóa được
                return self._get_fallback_card_info()  # Trả về thông tin mặc định

            # OPTIMIZED: Sử dụng session với connection pooling thay vì headers riêng lẻ
            
            # ENHANCED: Prompt chi tiết với hướng dẫn cụ thể
            ocr_prompt = """Extract business card text as JSON. For phone numbers, extract ONLY the numbers (remove TEL/MOBILE labels):
{"name":"","title":"","company":"","email":"","phone":"","website":"","address":"","other_info":""}

Instructions:
- name: Person's full name
- title: Job title/position
- company: Company name
- email: Email address
- phone: Phone numbers ONLY (remove TEL/MOBILE/Fax labels)
- website: Website URL
- address: Physical address
- other_info: Any additional information not covered above"""

            # Tạo payload cho API request - Create payload for API request (FASTEST: rút gọn)
            max_tokens = 2048
            payload = {
                "contents": [{
                    "parts": [
                        {"text": ocr_prompt},
                        {
                            "inline_data": {
                                "mime_type": "image/jpeg",
                                "data": card_data
                            }
                        }
                    ]
                }],
                "generationConfig": {
                    "temperature": 0.0,
                    "topK": 1,
                    "maxOutputTokens": max_tokens
                }
            }

            # PERFORMANCE MONITORING: Đo thời gian API call
            api_start_time = time.time()
            # OPTIMIZED: Gửi POST request đến Gemini API với session pooling - Send POST request to Gemini API with session pooling
            # HTTP/2 client (httpx) cho FASTEST nếu khả dụng, fallback requests.Session
            use_httpx = FASTEST_MODE
            response = None
            if use_httpx:
                try:
                    import httpx
                    with httpx.Client(http2=True, timeout=OCR_HTTP_TIMEOUT, headers={'Content-Type': 'application/json', 'Accept': 'application/json'}) as client:
                        r = client.post(
                            f"{self.base_url}/models/{self.model_name}:generateContent?key={self.gemini_key}",
                            json=payload
                        )
                        class _Resp:
                            def __init__(self, r):
                                self.status_code = r.status_code
                                self._r = r
                            def json(self):
                                return self._r.json()
                            @property
                            def text(self):
                                return self._r.text
                        response = _Resp(r)
                except Exception as _e:
                    print(f"⚠️ httpx http2 fallback to requests: {_e}")
                    use_httpx = False

            if not use_httpx:
                response = self.session.post(
                    f"{self.base_url}/models/{self.model_name}:generateContent?key={self.gemini_key}",
                    json=payload,
                    timeout=OCR_HTTP_TIMEOUT,
                    headers={'Content-Type': 'application/json', 'Accept': 'application/json'}
                )
            api_time = time.time() - api_start_time
            print(f"⏱️ Gemini API call time: {api_time:.2f}s")

            if response.status_code == 200:  # Nếu request thành công
                result = response.json()  # Parse JSON response

                if 'candidates' in result and result['candidates']:  # Nếu có candidates
                    try:
                        candidate = result['candidates'][0]
                        ocr_text = ""

                        if 'content' in candidate:
                            content = candidate['content']
                            if 'parts' in content and content['parts']:
                                parts = content['parts']
                                if parts and 'text' in parts[0]:
                                    ocr_text = parts[0]['text']
                            elif 'text' in content:
                                ocr_text = content['text']
                        elif 'text' in candidate:
                            ocr_text = candidate['text']

                        if not ocr_text:
                            raise KeyError("No text content found in response")

                        if not ocr_text or ocr_text.strip() == "":
                            return self._get_fallback_card_info()

                        card_info = self._parse_json_result(ocr_text)  # Gọi hàm parse JSON

                        fields_with_data = sum(1 for v in card_info.values() if v and v.strip())
                        if fields_with_data < 2:  # Chỉ warn nếu quá ít data
                            print(f"Low OCR quality: {fields_with_data}/7 fields")

                        # Lưu cache
                        self._set_cached_result(cache_key, card_info)

                        # Tổng thời gian
                        total_time = time.time() - total_start_time
                        print(f"⏱️ Stage 1 total time: {total_time:.2f}s (encode {encoding_time:.2f}s, api {api_time:.2f}s)")
                        return card_info  # Trả về thông tin card
                    except (KeyError, IndexError) as parse_error:
                        print(f"⚠️ Error parsing OCR response: {parse_error}")  # Log lỗi parse response
                        return self._get_fallback_card_info()  # Trả về thông tin mặc định
                else:
                    print("⚠️ No candidates in OCR response")  # Log không có candidates
                    return self._get_fallback_card_info()  # Trả về thông tin mặc định
            else:
                print(f"❌ Gemini OCR error: HTTP {response.status_code}")  # Log lỗi HTTP

                # Xử lý lỗi 429 (Rate Limit) cho OCR - Handle 429 (Rate Limit) for OCR
                if response.status_code == 429:
                    print("   🔄 OCR gặp lỗi giới hạn quota, đang thử API key dự phòng...")
                    if self.switch_to_backup_key():
                        print("   🔄 Đang thử lại OCR với API key dự phòng...")
                        try:
                            # Thử lại với key dự phòng - Retry with backup key
                            return self.extract_text_from_card(card_path)
                        except Exception as retry_error:
                            print(f"   ❌ OCR với API key dự phòng cũng thất bại: {retry_error}")
                    else:
                        print("   ❌ Tất cả OCR API keys đều hết quota")
                        print("   💡 Đề xuất: Chờ reset quota (24h) hoặc kiểm tra billing")

                if response.text:  # Nếu có response text
                    print(f"   Response: {response.text[:200]}...")  # Log 200 ký tự đầu của response
                total_time = time.time() - total_start_time
                print(f"⏱️ Stage 1 total time (error path): {total_time:.2f}s")
                return self._get_fallback_card_info()  # Trả về thông tin mặc định

        except Exception as e:
            print(f"❌ Gemini OCR exception: {e}")  # Log exception OCR

            # Kiểm tra xem có phải lỗi rate limit không - Check if it's a rate limit error
            error_str = str(e).lower()
            if 'rate limit' in error_str or 'quota' in error_str or '429' in error_str:
                print("   🔄 OCR exception chứa lỗi quota, đang thử API key dự phòng...")
                if self.switch_to_backup_key():
                    print("   🔄 Đang thử lại OCR với API key dự phòng...")
                    try:
                        # Thử lại với key dự phòng - Retry with backup key
                        return self.extract_text_from_card(card_path)
                    except Exception as retry_error:
                        print(f"   ❌ OCR retry với API key dự phòng cũng thất bại: {retry_error}")
                else:
                    print("   ❌ Tất cả OCR API keys đều hết quota")

            total_time = time.time() - total_start_time
            print(f"⏱️ Stage 1 total time (exception path): {total_time:.2f}s")
            return self._get_fallback_card_info()  # Trả về thông tin mặc định

    def _parse_json_result(self, ocr_text):
        """Parse kết quả JSON từ Gemini OCR với fallback thông minh - Parse JSON result from Gemini OCR with smart fallback"""

        try:
            cleaned_text = ocr_text.strip()

            if cleaned_text.startswith('```json'):
                cleaned_text = cleaned_text[7:]
            elif cleaned_text.startswith('```'):
                cleaned_text = cleaned_text[3:]

            if cleaned_text.endswith('```'):
                cleaned_text = cleaned_text[:-3]

            cleaned_text = cleaned_text.strip()

            start = cleaned_text.find('{')
            end = cleaned_text.rfind('}') + 1

            if start >= 0 and end > start:
                json_str = cleaned_text[start:end]

                try:
                    parsed = json.loads(json_str)

                    # Làm sạch và validate dữ liệu đã parse - Clean and validate the parsed data
                    def clean_json_value(value):
                        """Clean JSON value nếu nó chứa JSON string"""
                        if not value:
                            return value
                        value = str(value).strip()
                        # Nếu value chứa JSON pattern như '"key": "value"', extract value
                        if '":' in value and value.count('"') >= 2:
                            # Tìm phần sau dấu : cuối cùng
                            parts = value.split('":')
                            if len(parts) > 1:
                                last_part = parts[-1].strip()
                                # Loại bỏ dấu phẩy và ngoặc kép cuối
                                last_part = last_part.rstrip(',"').strip('"')
                                return last_part
                        return value
                    
                    card_info = {
                        'name': clean_json_value(parsed.get('name', '')),  # Tên - làm sạch khoảng trắng
                        'title': clean_json_value(parsed.get('title', '')),  # Chức vụ - làm sạch khoảng trắng
                        'company': clean_json_value(parsed.get('company', '')),  # Công ty - làm sạch khoảng trắng
                        'email': clean_json_value(parsed.get('email', '')),  # Email - làm sạch khoảng trắng
                        'phone': clean_json_value(parsed.get('phone', '')),  # Điện thoại - làm sạch khoảng trắng
                        'website': clean_json_value(parsed.get('website', '')),  # Website - làm sạch khoảng trắng
                        'address': clean_json_value(parsed.get('address', '')),  # Địa chỉ - làm sạch khoảng trắng
                        'other_info': clean_json_value(parsed.get('other_info', ''))  # Thông tin khác - làm sạch khoảng trắng
                    }

                    # Kiểm tra chất lượng OCR - Check OCR quality
                    non_empty_fields = sum(1 for v in card_info.values() if v and str(v).strip())
                    total_fields = len(card_info)

                    print(f"📊 OCR Quality Check: {non_empty_fields}/{total_fields} fields extracted")

                    # Nếu không có field nào được extract, báo lỗi thay vì fake data
                    if non_empty_fields == 0:
                        print("❌ OCR failed completely - no fields extracted")
                        print("🔄 Trying to re-process with enhanced settings...")
                        # Có thể thử lại với settings khác hoặc trả về lỗi
                        return None  # Trả về None thay vì fake data

                    # Chỉ giữ lại data thật, không điền fake data
                    # Only keep real data, don't fill fake data

                    if card_info is None:
                        print("❌ OCR extraction completely failed")
                        return self._get_fallback_card_info()  # Trả về fallback

                    print("✅ JSON parsing successful")  # Log parse JSON thành công
                    self._print_card_info(card_info)  # In thông tin card
                    return card_info  # Trả về thông tin card

                except json.JSONDecodeError:
                    print("⚠️ JSON decode failed, using smart text parsing")  # Log JSON decode thất bại
                    return self._smart_text_parsing(ocr_text)  # Chuyển sang parse text thông minh
            else:
                print("⚠️ No JSON found, using smart text parsing")  # Log không tìm thấy JSON
                return self._smart_text_parsing(ocr_text)  # Chuyển sang parse text thông minh

        except Exception as e:
            print(f"⚠️ Parsing error: {e}")  # Log lỗi parsing
            return self._smart_text_parsing(ocr_text)  # Chuyển sang parse text thông minh

    def _smart_text_parsing(self, ocr_text):
        """Parse text thông minh khi JSON thất bại - Smart text parsing when JSON fails"""

        try:
            print("🔍 Using smart text parsing...")  # Log sử dụng parse text thông minh

            # Khởi tạo với các field rỗng - KHÔNG CÓ GIÁ TRỊ MẶC ĐỊNH! - Initialize with empty fields - NO DEFAULTS!
            card_info = {
                'name': '',  # Tên rỗng
                'title': '',  # Chức vụ rỗng
                'company': '',  # Công ty rỗng
                'email': '',  # Email rỗng
                'phone': '',  # Điện thoại rỗng
                'website': '',  # Website rỗng
                'address': ''  # Địa chỉ rỗng
            }

            # Trích xuất thông tin sử dụng pattern nâng cao - Extract information using advanced patterns
            import re  # Import thư viện regex

            # Debug: Kiểm tra OCR text trước khi split - Debug: Check OCR text before splitting
            print(f"📄 OCR text length: {len(ocr_text)} characters")
            if len(ocr_text) <= 200:
                print(f"📄 OCR text content: '{ocr_text}'")

            lines = [line.strip() for line in ocr_text.split('\n') if line.strip()]  # Tách thành các dòng và loại bỏ dòng trống

            print(f"📄 Analyzing {len(lines)} lines of extracted text...")  # Log số dòng text cần phân tích

            # Debug: Nếu không có dòng nào, thử split bằng cách khác - Debug: If no lines, try different splitting
            if len(lines) == 0:
                print("⚠️ No lines found after splitting by \\n, trying alternative methods...")
                # Thử split bằng space hoặc comma
                words = [word.strip() for word in ocr_text.split() if word.strip()]
                if len(words) > 0:
                    print(f"📄 Found {len(words)} words when splitting by space")
                    lines = [' '.join(words[i:i+3]) for i in range(0, len(words), 3)]  # Nhóm 3 từ thành 1 dòng
                    print(f"📄 Created {len(lines)} artificial lines from words")
                else:
                    print("⚠️ No words found either, OCR may have failed completely")
                    return self._get_fallback_card_info()

            for i, line in enumerate(lines):  # Lặp qua từng dòng
                line_lower = line.lower()  # Chuyển thành chữ thường để so sánh
                print(f"   Line {i+1}: '{line}'")  # Log từng dòng

                # Trích xuất email với nhiều pattern - Email extraction with multiple patterns
                if not card_info['email']:  # Nếu chưa tìm thấy email
                    email_patterns = [  # Danh sách pattern email
                        r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',  # Pattern email chuẩn
                        r'[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}',  # Pattern email đơn giản
                    ]

                    for pattern in email_patterns:  # Lặp qua từng pattern
                        emails = re.findall(pattern, line, re.IGNORECASE)  # Tìm email trong dòng
                        if emails:  # Nếu tìm thấy email
                            card_info['email'] = emails[0]  # Lấy email đầu tiên
                            print(f"      ✅ Found email: {emails[0]}")  # Log email tìm thấy
                            break  # Thoát khỏi vòng lặp

                # Trích xuất số điện thoại với pattern Việt Nam - Phone extraction with Vietnamese patterns
                if not card_info['phone'] and any(char.isdigit() for char in line):  # Nếu chưa có phone và dòng có số
                    phone_patterns = [  # Danh sách pattern điện thoại
                        r'(\+84|0)[0-9\s\-\(\)\.]{8,15}',  # Pattern điện thoại Việt Nam - Vietnamese phone patterns
                        r'[0-9\s\-\(\)\.]{9,15}',          # Pattern điện thoại chung - General phone patterns
                    ]

                    for pattern in phone_patterns:  # Lặp qua từng pattern
                        phones = re.findall(pattern, line)  # Tìm số điện thoại trong dòng
                        if phones:  # Nếu tìm thấy số điện thoại
                            # Làm sạch và validate số điện thoại - Clean and validate phone number
                            phone = phones[0].strip()  # Loại bỏ khoảng trắng
                            # Loại bỏ các label như TEL, MOBILE, FAX
                            phone = re.sub(r'\b(TEL|MOBILE|FAX|PHONE|CELL|MOB)\b[:\s]*', '', phone, flags=re.IGNORECASE)
                            digits_only = re.sub(r'[^\d]', '', phone)  # Chỉ giữ lại số
                            if len(digits_only) >= 9:  # Độ dài tối thiểu của số điện thoại - Minimum phone length
                                card_info['phone'] = phone.strip()  # Lưu số điện thoại đã clean
                                print(f"      ✅ Found phone: {phone.strip()}")  # Log số điện thoại tìm thấy
                                break  # Thoát khỏi vòng lặp

                # Trích xuất website - Website extraction
                if not card_info['website']:  # Nếu chưa tìm thấy website
                    website_patterns = [  # Danh sách pattern website
                        r'(https?://)?[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}',  # Pattern URL đầy đủ
                        r'www\.[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}',  # Pattern www
                    ]

                    for pattern in website_patterns:  # Lặp qua từng pattern
                        websites = re.findall(pattern, line, re.IGNORECASE)  # Tìm website trong dòng
                        if websites:  # Nếu tìm thấy website
                            card_info['website'] = websites[0]  # Lấy website đầu tiên
                            print(f"      ✅ Found website: {websites[0]}")  # Log website tìm thấy
                            break  # Thoát khỏi vòng lặp

                # Phát hiện tên công ty - Company name detection
                if not card_info['company']:  # Nếu chưa tìm thấy công ty
                    # Danh sách từ khóa chỉ báo công ty
                    company_indicators = ['company', 'corp', 'ltd', 'inc', 'co.', 'llc', 'solution', 'tech', 'software', 'group', 'enterprise']
                    if any(indicator in line_lower for indicator in company_indicators):  # Nếu dòng chứa từ khóa công ty
                        # Bỏ qua nếu rõ ràng không phải tên công ty - Skip if it's clearly not a company name
                        if not any(skip in line_lower for skip in ['email', 'phone', 'website', '@', 'www']):  # Nếu không chứa từ khóa loại trừ
                            card_info['company'] = line  # Lưu tên công ty
                            print(f"      ✅ Found company: {line}")  # Log công ty tìm thấy

                # Phát hiện tên người (thường xuất hiện sớm, 2-4 từ, không có số) - Name detection (usually appears early, 2-4 words, no numbers)
                if not card_info['name'] and len(line.split()) >= 2 and len(line.split()) <= 4:  # Nếu chưa có tên và dòng có 2-4 từ
                    # Kiểm tra xem có giống tên người không - Check if it looks like a person's name
                    if (not any(char.isdigit() for char in line) and  # Không chứa số
                        not '@' in line and  # Không chứa @
                        not any(indicator in line_lower for indicator in ['company', 'corp', 'ltd', 'manager', 'director', 'engineer', 'www', '.com'])):  # Không chứa từ khóa loại trừ
                        card_info['name'] = line  # Lưu tên người
                        print(f"      ✅ Found name: {line}")  # Log tên tìm thấy

                # Phát hiện chức vụ (job titles) - Title detection (job titles)
                if not card_info['title']:  # Nếu chưa tìm thấy chức vụ
                    # Danh sách từ khóa chức vụ
                    title_keywords = ['manager', 'director', 'engineer', 'developer', 'analyst', 'specialist', 'coordinator', 'assistant', 'executive', 'officer', 'lead', 'senior', 'junior', 'head', 'chief', 'president', 'ceo', 'cto', 'cfo']
                    if any(keyword in line_lower for keyword in title_keywords):  # Nếu dòng chứa từ khóa chức vụ
                        if not any(skip in line_lower for skip in ['company', '@', 'www', '.com']):  # Nếu không chứa từ khóa loại trừ
                            card_info['title'] = line  # Lưu chức vụ
                            print(f"      ✅ Found title: {line}")  # Log chức vụ tìm thấy

                # Phát hiện địa chỉ (thường là dòng dài với chỉ báo vị trí) - Address detection (usually longer lines with location indicators)
                if not card_info['address']:  # Nếu chưa tìm thấy địa chỉ
                    # Danh sách từ khóa chỉ báo địa chỉ
                    address_indicators = ['street', 'st.', 'avenue', 'ave', 'road', 'rd', 'district', 'city', 'vietnam', 'vn', 'address']
                    if any(indicator in line_lower for indicator in address_indicators):  # Nếu dòng chứa từ khóa địa chỉ
                        card_info['address'] = line  # Lưu địa chỉ
                        print(f"      ✅ Found address: {line}")  # Log địa chỉ tìm thấy

            print("✅ Smart text parsing completed")  # Log hoàn thành parse text thông minh
            
            # Clean JSON values nếu có
            def clean_json_value(value):
                if not value:
                    return value
                value = str(value).strip()
                # Nếu value chứa JSON pattern như '"key": "value"', extract value
                if '":' in value and value.count('"') >= 2:
                    # Tìm phần sau dấu : cuối cùng
                    parts = value.split('":')
                    if len(parts) > 1:
                        last_part = parts[-1].strip()
                        # Loại bỏ dấu phẩy và ngoặc kép cuối
                        last_part = last_part.rstrip(',"').strip('"')
                        return last_part
                return value
            
            card_info = {
                'name': clean_json_value(card_info.get('name', '')),
                'title': clean_json_value(card_info.get('title', '')),
                'company': clean_json_value(card_info.get('company', '')),
                'email': clean_json_value(card_info.get('email', '')),
                'phone': clean_json_value(card_info.get('phone', '')),
                'website': clean_json_value(card_info.get('website', '')),
                'address': clean_json_value(card_info.get('address', '')),
                'other_info': clean_json_value(card_info.get('other_info', ''))
            }
            
            self._print_card_info(card_info)  # In thông tin card đã trích xuất
            return card_info  # Trả về thông tin card

        except Exception as e:
            print(f"⚠️ Smart parsing error: {e}")  # Log lỗi parse thông minh
            return self._get_fallback_card_info()  # Trả về thông tin mặc định

    def _print_card_info(self, card_info):
        """In thông tin card theo định dạng sạch - Print card information in clean format"""
        print(f"   📄 Extracted Card Information")  # Header thông tin đã trích xuất
        print(f"   Name: {card_info['name']}")  # In tên
        print(f"   Title: {card_info['title']}")  # In chức vụ
        print(f"   Company: {card_info['company']}")  # In công ty
        print(f"   Email: {card_info['email']}")  # In email
        print(f"   Phone: {card_info['phone']}")  # In điện thoại
        print(f"   Website: {card_info['website']}")  # In website
        print(f"   Address: {card_info['address']}")  # In địa chỉ
        print(f"   Other Info: {card_info['other_info']}")  # In thông tin khác

    def _get_fallback_card_info(self):
        """Thông tin card dự phòng khi OCR thất bại - Fallback card info when OCR fails"""
        print("⚠️ Using fallback card info - OCR extraction failed")
        return {
            'name': '',  # Để trống thay vì fake data
            'title': '',  # Để trống thay vì fake data
            'company': '',  # Để trống thay vì fake data
            'email': '',  # Để trống thay vì fake data
            'phone': '',  # Để trống thay vì fake data
            'address': '',  # Để trống thay vì fake data
            'website': '',  # Để trống thay vì fake data
            'additional_info': 'OCR extraction failed - please try again with better lighting'  # Thông tin bổ sung
        }
    
    def _encode_image(self, image_path):
        """Mã hóa ảnh thành base64 cho Gemini API với tiền xử lý để OCR tốt hơn - Encode image to base64 for Gemini API with preprocessing for better OCR"""

        try:
            if not Path(image_path).exists():  # Nếu file ảnh không tồn tại
                print(f"❌ Image not found: {image_path}")  # Log không tìm thấy ảnh
                return None  # Trả về None

            # Import PIL để tiền xử lý ảnh - Import PIL for image preprocessing
            from PIL import Image, ImageEnhance, ImageFilter, ImageOps  # Import các class xử lý ảnh
            import io  # Import thư viện IO
            import numpy as np
            import cv2

            # Mở và tiền xử lý ảnh để OCR tốt hơn - Open and preprocess image for better OCR
            with Image.open(image_path) as img:  # Mở ảnh với context manager
                print(f"📸 Original image size: {img.size}")  # Log kích thước ảnh gốc

                # Chuyển đổi sang RGB nếu cần - Convert to RGB if needed
                if img.mode != 'RGB':  # Nếu không phải chế độ RGB
                    img = img.convert('RGB')  # Chuyển đổi sang RGB

                # Sửa orientation theo EXIF nếu có - Fix orientation from EXIF
                try:
                    img = ImageOps.exif_transpose(img)
                except Exception:
                    pass

                # Heuristic đánh giá độ mờ để quyết định tăng chất lượng - Blur heuristic
                try:
                    img_small = img.copy().resize((min(640, img.width), int(img.height * min(640, img.width) / img.width)))
                    gray = cv2.cvtColor(np.array(img_small), cv2.COLOR_RGB2GRAY)
                    lap_var = cv2.Laplacian(gray, cv2.CV_64F).var()
                except Exception:
                    lap_var = 9999.0  # nếu không tính được thì coi như sắc nét
                print(f"🔎 Blur (var of Laplacian): {lap_var:.1f}")

                boost_quality = (lap_var < 120.0) or (max(img.size) < 900)
                if boost_quality:
                    print("🔁 BOOST quality preprocessing enabled (low sharpness or small size)")

                # OPTIMIZED: Resize để có độ phân giải tối ưu cho OCR
                max_size = max(OCR_MAX_SIZE, 1280) if boost_quality else OCR_MAX_SIZE
                min_size = max(OCR_MIN_SIZE, 800) if boost_quality else OCR_MIN_SIZE

                current_max = max(img.size)
                if current_max < min_size:  # Nếu ảnh quá nhỏ
                    # Phóng to ảnh nhỏ lên ít nhất 800px - Upscale small images to at least 800px
                    scale_factor = min_size / current_max  # Tính hệ số phóng to
                    new_size = (int(img.width * scale_factor), int(img.height * scale_factor))  # Kích thước mới
                    img = img.resize(new_size, Image.Resampling.LANCZOS)  # Resize với thuật toán LANCZOS
                    print(f"📈 Upscaled from {current_max}px to: {img.size}")  # Log kích thước sau khi phóng to
                elif current_max > max_size:  # Nếu ảnh lớn hơn kích thước tối đa
                    # Thu nhỏ ảnh lớn - Downscale large images
                    scale_factor = max_size / current_max  # Tính hệ số thu nhỏ
                    new_size = (int(img.width * scale_factor), int(img.height * scale_factor))  # Kích thước mới
                    img = img.resize(new_size, Image.Resampling.LANCZOS)  # Resize với thuật toán LANCZOS
                    print(f"📉 Downscaled from {current_max}px to: {img.size}")  # Log kích thước sau khi thu nhỏ
                else:
                    print(f"✅ Image size optimal: {img.size}")  # Log kích thước đã tối ưu

                # OPTIMIZED: Nâng cao ảnh tối ưu cho OCR nhanh - Optimized image enhancement for fast OCR
                print("🔧 Applying optimized image enhancements...")  # Log bắt đầu nâng cao ảnh

                # Bước 1: (option) Brightness
                if OCR_BRIGHTNESS and abs(OCR_BRIGHTNESS - 1.0) > 1e-3:
                    enhancer = ImageEnhance.Brightness(img)
                    img = enhancer.enhance(OCR_BRIGHTNESS)

                # Bước 2: Contrast
                enhancer = ImageEnhance.Contrast(img)
                img = enhancer.enhance(1.5 if boost_quality else OCR_CONTRAST)

                # Bước 3: Sharpness
                enhancer = ImageEnhance.Sharpness(img)
                img = enhancer.enhance(1.4 if boost_quality else OCR_SHARPNESS)

                # Bước 4: (optional) Unsharp mask
                if OCR_ENABLE_UNSHARP or boost_quality:
                    radius = max(1.1, OCR_UNSHARP_RADIUS) if boost_quality else OCR_UNSHARP_RADIUS
                    percent = max(160, OCR_UNSHARP_PERCENT) if boost_quality else OCR_UNSHARP_PERCENT
                    threshold = OCR_UNSHARP_THRESHOLD
                    img = img.filter(ImageFilter.UnsharpMask(radius=radius, percent=percent, threshold=threshold))

                print(f"✨ Optimized image enhancement completed")  # Log hoàn thành nâng cao ảnh
                print(f"   - Brightness: {OCR_BRIGHTNESS}x")
                print(f"   - Contrast: {OCR_CONTRAST}x")
                print(f"   - Sharpness: {OCR_SHARPNESS}x")
                if OCR_ENABLE_UNSHARP:
                    print(f"   - Unsharp mask applied (r={OCR_UNSHARP_RADIUS}, p={OCR_UNSHARP_PERCENT}, t={OCR_UNSHARP_THRESHOLD})")

                # OPTIMIZED: Chuyển đổi thành bytes với compression tối ưu - Convert to bytes with optimized compression
                img_buffer = io.BytesIO()  # Tạo buffer IO
                q = max(80, OCR_JPEG_QUALITY) if boost_quality else OCR_JPEG_QUALITY
                subs = 1 if boost_quality else OCR_JPEG_SUBSAMPLING
                img.save(img_buffer, format='JPEG', quality=q, optimize=True, subsampling=subs)
                img_buffer.seek(0)  # Đặt con trỏ về đầu buffer

                # Mã hóa thành base64 - Encode to base64
                image_data = base64.b64encode(img_buffer.getvalue()).decode('utf-8')  # Mã hóa base64
                print(f"📦 Image encoded: {len(image_data)} characters (optimized compression)")  # Log độ dài dữ liệu đã mã hóa

                return image_data  # Trả về dữ liệu ảnh đã mã hóa

        except Exception as e:
            print(f"❌ Image encoding error: {e}")  # Log lỗi mã hóa ảnh
            # Fallback sang mã hóa đơn giản - Fallback to simple encoding
            try:
                with open(image_path, 'rb') as f:  # Mở file ở chế độ binary
                    image_data = base64.b64encode(f.read()).decode('utf-8')  # Mã hóa base64 đơn giản
                print(f"📦 Fallback encoding: {len(image_data)} characters")  # Log mã hóa fallback
                return image_data  # Trả về dữ liệu đã mã hóa
            except:
                return None  # Trả về None nếu tất cả đều thất bại

    # -------------------------------
    # Simple cache helpers
    # -------------------------------
    def _build_cache_key(self, image_path: str) -> Optional[str]:
        try:
            p = Path(image_path)
            if not p.exists():
                return None
            stat = p.stat()
            return f"{str(p.resolve())}|{int(stat.st_mtime)}|{stat.st_size}"
        except Exception:
            return None

    def _get_cached_result(self, key: Optional[str]) -> Optional[dict]:
        if not key:
            return None
        try:
            entry = self._cache.get(key)
            if not entry:
                return None
            ts, data = entry
            if (time.time() - ts) <= self._cache_ttl:
                return data
            else:
                # expired
                self._cache.pop(key, None)
                return None
        except Exception:
            return None

    def _set_cached_result(self, key: Optional[str], data: dict) -> None:
        if not key or not isinstance(data, dict):
            return
        try:
            self._cache[key] = (time.time(), data)
        except Exception:
            pass

def test_gemini_ocr_service():
    """Test dịch vụ Gemini OCR - Test Gemini OCR Service"""
    print("🧪 Testing Gemini OCR Service")  # Log bắt đầu test
    print("=" * 50)  # In dòng phân cách

    ocr = GeminiOCRService()  # Tạo instance OCR service

    # Test với name card - Test with business card
    card_path = "card1.jpg"  # Đường dẫn ảnh test
    if Path(card_path).exists():  # Nếu file test tồn tại
        print(f"\n📄 Testing OCR with: {card_path}")  # Log đang test với file

        result = ocr.extract_text_from_card(card_path)  # Gọi hàm trích xuất text

        print("✅ OCR Result:")  # Log kết quả OCR
        for key, value in result.items():  # Lặp qua từng cặp key-value
            print(f"   {key}: {value}")  # In từng thông tin
    else:
        print("❌ No test card image found")  # Log không tìm thấy ảnh test

if __name__ == "__main__":
    test_gemini_ocr_service()
