/* Search Database Styles - <PERSON><PERSON> cách cho giao diện tìm kiếm database */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

/* Navigation Bar */
.apple-nav {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
    padding: 0 20px;
}

.apple-nav-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 60px;
}

.apple-logo {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: #333;
}

.nav-logo-img {
    height: 32px;
    margin-right: 12px;
}

.nav-title {
    font-size: 18px;
    font-weight: 600;
}

.nav-actions {
    display: flex;
    gap: 15px;
}

.nav-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: #007AFF;
    color: white;
    text-decoration: none;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.nav-btn:hover {
    background: #0056CC;
    transform: translateY(-1px);
}

/* Main Container */
.search-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* Header Section */
.search-header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

.search-header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.search-header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Search Controls */
.search-controls {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 16px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.search-form {
    max-width: 1200px;
    margin: 0 auto;
}

.search-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    align-items: end;
}

.search-row:last-child {
    margin-bottom: 0;
}

.search-field {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.search-field label {
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.search-input {
    padding: 12px 16px;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    font-size: 16px;
    transition: all 0.3s ease;
    width: 100%;
}

.search-input:focus {
    outline: none;
    border-color: #007AFF;
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.date-input {
    font-family: inherit;
}

.search-actions {
    display: flex;
    gap: 12px;
    align-items: end;
}

.search-btn, .clear-btn {
    padding: 12px 20px;
    border: none;
    border-radius: 10px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.search-btn {
    background: #007AFF;
    color: white;
}

.search-btn:hover {
    background: #0056CC;
    transform: translateY(-1px);
}

.clear-btn {
    background: #FF3B30;
    color: white;
}

.clear-btn:hover {
    background: #D70015;
    transform: translateY(-1px);
}

/* Tables Container */
.tables-container {
    display: grid;
    gap: 30px;
}

.table-section {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.table-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.table-header h2 {
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.table-controls {
    display: flex;
    align-items: center;
    gap: 15px;
}

.sort-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 8px 16px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
}

.sort-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

.table-info {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* Table Wrapper */
.table-wrapper {
    overflow-x: auto;
    max-height: 600px;
    position: relative;
}

/* Loading State */
.loading {
    text-align: center;
    padding: 40px;
    font-size: 1.1rem;
    color: #666;
}

.loading i {
    margin-right: 10px;
    color: #007AFF;
}

/* Data Table */
.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.data-table th {
    background: #f8f9fa;
    padding: 12px 8px;
    text-align: left;
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
    position: sticky;
    top: 0;
    z-index: 10;
}

.data-table td {
    padding: 10px 8px;
    border-bottom: 1px solid #dee2e6;
    vertical-align: top;
}

.data-table tr:hover {
    background-color: #f8f9fa;
}

.data-table td {
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Image Links */
.image-link {
    color: #007AFF;
    text-decoration: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.image-link:hover {
    text-decoration: underline;
}

/* Pagination */
.pagination-container {
    padding: 20px;
    border-top: 1px solid #dee2e6;
}

.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
}

.pagination button {
    padding: 8px 12px;
    border: 1px solid #dee2e6;
    background: white;
    color: #333;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.pagination button:hover:not(:disabled) {
    background: #007AFF;
    color: white;
    border-color: #007AFF;
}

.pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination button.active {
    background: #007AFF;
    color: white;
    border-color: #007AFF;
}

.pagination-info {
    margin: 0 15px;
    font-size: 14px;
    color: #666;
}

/* Modal Styles */
.modal {
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    overflow: hidden;
}

.image-modal-content {
    max-width: 800px;
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.close {
    color: white;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    line-height: 1;
}

.close:hover {
    opacity: 0.7;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    padding: 15px 20px;
    border-top: 1px solid #dee2e6;
    text-align: right;
}

.image-preview {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .search-container {
        padding: 15px;
    }

    .search-row {
        flex-direction: column;
        gap: 15px;
    }

    .search-actions {
        flex-direction: row;
        justify-content: center;
        gap: 10px;
    }

    .apple-nav-content {
        flex-direction: column;
        height: auto;
        padding: 15px 0;
        gap: 15px;
    }

    .search-header h1 {
        font-size: 2rem;
    }

    .table-header {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }

    .pagination {
        gap: 5px;
    }

    .pagination button {
        padding: 6px 10px;
        font-size: 12px;
    }

    .data-table {
        font-size: 12px;
    }

    .data-table th,
    .data-table td {
        padding: 8px 4px;
        max-width: 100px;
    }
}
