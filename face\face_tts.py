import os
import re
import time
import uuid
from pathlib import Path
from typing import Optional, Dict
import logging

try:
    from gtts import gTTS
    GTTS_AVAILABLE = True
except ImportError:
    GTTS_AVAILABLE = False
    print("⚠️ gTTS chưa được cài đặt. Chạy: pip install gtts")

try:
    import pygame
    PYGAME_AVAILABLE = True
except ImportError:
    PYGAME_AVAILABLE = False
    print("⚠️ pygame chưa được cài đặt. Chạy: pip install pygame")

logger = logging.getLogger(__name__)

class FaceTTS:
    
    def __init__(self, audio_dir: str = "face/audio", language: str = "en"):
        self.audio_dir = Path(audio_dir)
        self.audio_dir.mkdir(parents=True, exist_ok=True)
        
        self.language = language
        self.audio_cache: Dict[str, str] = {}
        
        self.default_messages = {
            "welcome": "Please proceed to the check-in area",
            "no_face": "I can't see your face yet, please look at the camera",
            "multiple_faces": "More than one face detected",
            "face_too_far": "Please move your face a bit closer to the camera",
            "keep_face": "Great, please keep your face like that",
            "face_lost_countdown": "Face lost during countdown",
            "capture_success": "Photo captured successfully, thank you",
        }
        
        if not GTTS_AVAILABLE:
            logger.warning("⚠️ gTTS chưa có sẵn - Tính năng TTS bị vô hiệu hóa")
        
        if not PYGAME_AVAILABLE:
            logger.warning("⚠️ pygame chưa có sẵn - Tính năng phát âm bị vô hiệu hóa")
    
    def generate_audio_filename(self, text: str) -> str:
        # Chỉ sử dụng hash của text để tạo tên file duy nhất
        # Không cần timestamp và UUID vì cùng text sẽ có cùng hash
        text_hash = str(hash(text))[-8:]
        return f"face_tts_{text_hash}.mp3"
    
    def text_to_speech(self, text: str, slow: bool = False) -> Optional[str]:
        if not GTTS_AVAILABLE:
            return None
            
        if not text or not text.strip():
            return None
            
        text = text.strip()
        
        # Tạo tên file dựa trên text
        filename = self.generate_audio_filename(text)
        audio_path = self.audio_dir / filename
        
        # Kiểm tra cache trước
        cache_key = f"{text}_{self.language}_{slow}"
        if cache_key in self.audio_cache:
            cached_path = self.audio_cache[cache_key]
            if os.path.exists(cached_path):
                return cached_path
            else:
                del self.audio_cache[cache_key]
        
        # Kiểm tra file đã tồn tại trên disk
        if os.path.exists(audio_path):
            # File đã tồn tại, thêm vào cache và trả về
            self.audio_cache[cache_key] = str(audio_path)
            logger.info(f"✅ Tái sử dụng file TTS có sẵn: {text} -> {audio_path}")
            return str(audio_path)
        
        # File chưa tồn tại, tạo mới
        try:
            tts = gTTS(text=text, lang=self.language, slow=slow)
            tts.save(str(audio_path))
            
            self.audio_cache[cache_key] = str(audio_path)
            
            logger.info(f"✅ Tạo TTS mới thành công: {text} -> {audio_path}")
            return str(audio_path)
            
        except Exception as e:
            logger.error(f"❌ Lỗi khi tạo TTS: {e}")
            return None
    
    def speak_message(self, message_key: str, custom_text: str = None) -> Optional[str]:
        if custom_text:
            return self.text_to_speech(custom_text)
        
        if message_key in self.default_messages:
            return self.text_to_speech(self.default_messages[message_key])
        
        return None
    
    def speak_no_face(self) -> Optional[str]:
        return self.speak_message("no_face")
    
    def speak_multiple_faces(self) -> Optional[str]:
        return self.speak_message("multiple_faces")
    
    def speak_face_too_far(self) -> Optional[str]:
        return self.speak_message("face_too_far")
    
    def speak_face_direction(self, direction: str) -> Optional[str]:
        direction_map = {
            "left": "face_left",
            "right": "face_right", 
            "up": "face_up",
            "down": "face_down"
        }
        
        if direction in direction_map:
            return self.speak_message(direction_map[direction])
        
        return None
    
    def speak_keep_face(self) -> Optional[str]:
        return self.speak_message("keep_face")
    
    def speak_face_lost_countdown(self) -> Optional[str]:
        return self.speak_message("face_lost_countdown")
    
    def speak_capture_success(self) -> Optional[str]:
        return self.speak_message("capture_success")
    
    def speak_welcome(self) -> Optional[str]:
        return self.speak_message("welcome")
    
    def clear_cache(self):
        self.audio_cache.clear()
        logger.info("✅ Đã xóa cache TTS")
    
    def get_cache_info(self) -> Dict[str, str]:
        return self.audio_cache.copy()
    
    def cleanup_old_audio_files(self, keep_days: int = 7):
        try:
            current_time = time.time()
            cutoff_time = current_time - (keep_days * 24 * 60 * 60)
            
            cleaned_count = 0
            for audio_file in self.audio_dir.glob("face_tts_*.mp3"):
                if audio_file.stat().st_mtime < cutoff_time:
                    try:
                        audio_file.unlink()
                        cleaned_count += 1
                        logger.info(f" Đã xóa file audio cũ: {audio_file.name}")
                    except Exception as e:
                        logger.warning(f" Không thể xóa file: {audio_file.name}, lỗi: {e}")
            
            if cleaned_count > 0:
                logger.info(f" Đã dọn dẹp {cleaned_count} file audio cũ")
            
            return cleaned_count
            
        except Exception as e:
            logger.error(f" Lỗi khi dọn dẹp file audio: {e}")
            return 0
    
    def get_audio_stats(self) -> Dict[str, any]:
        try:
            audio_files = list(self.audio_dir.glob("face_tts_*.mp3"))
            total_size = sum(f.stat().st_size for f in audio_files)
            
            return {
                'total_files': len(audio_files),
                'total_size_bytes': total_size,
                'total_size_mb': round(total_size / (1024 * 1024), 2),
                'cache_entries': len(self.audio_cache)
            }
        except Exception as e:
            logger.error(f" Lỗi khi lấy thống kê audio: {e}")
            return {}
    
    def play_audio(self, audio_path: str) -> bool:
        if not PYGAME_AVAILABLE:
            logger.warning("⚠️ pygame chưa có sẵn - không thể phát âm")
            return False
            
        if not audio_path or not os.path.exists(audio_path):
            logger.warning(f"⚠️ File audio không tồn tại: {audio_path}")
            return False
            
        try:
            # Khởi tạo pygame mixer nếu chưa có
            if not pygame.mixer.get_init():
                pygame.mixer.init(frequency=22050, size=-16, channels=2, buffer=512)
                logger.info("🔧 Đã khởi tạo pygame mixer")
            
            # Dừng audio đang phát (nếu có)
            if pygame.mixer.music.get_busy():
                pygame.mixer.music.stop()
            
            # Load và phát audio mới
            pygame.mixer.music.load(audio_path)
            pygame.mixer.music.play()
            
            logger.info(f"🔊 Đang phát âm: {audio_path}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Lỗi khi phát audio: {e}")
            return False

face_tts = FaceTTS()

def speak_no_face():
    return face_tts.speak_no_face()

def speak_multiple_faces():
    return face_tts.speak_multiple_faces()

def speak_face_too_far():
    return face_tts.speak_face_too_far()

def speak_face_direction(direction: str):
    return face_tts.speak_face_direction(direction)

def speak_keep_face():
    return face_tts.speak_keep_face()

def speak_face_lost_countdown():
    return face_tts.speak_face_lost_countdown()

def speak_capture_success():
    return face_tts.speak_capture_success()

def speak_welcome():
    return face_tts.speak_welcome()

def speak_custom(text: str):
    return face_tts.text_to_speech(text)

# Các hàm vừa tạo vừa phát âm
def speak_and_play_no_face():
    audio_path = face_tts.speak_no_face()
    if audio_path:
        face_tts.play_audio(audio_path)
    return audio_path

def speak_and_play_multiple_faces():
    audio_path = face_tts.speak_multiple_faces()
    if audio_path:
        face_tts.play_audio(audio_path)
    return audio_path

def speak_and_play_face_too_far():
    audio_path = face_tts.speak_face_too_far()
    if audio_path:
        face_tts.play_audio(audio_path)
    return audio_path

def speak_and_play_face_direction(direction: str):
    audio_path = face_tts.speak_face_direction(direction)
    if audio_path:
        face_tts.play_audio(audio_path)
    return audio_path

def speak_and_play_keep_face():
    audio_path = face_tts.speak_keep_face()
    if audio_path:
        face_tts.play_audio(audio_path)
    return audio_path

def speak_and_play_face_lost_countdown():
    audio_path = face_tts.speak_face_lost_countdown()
    if audio_path:
        face_tts.play_audio(audio_path)
    return audio_path

def speak_and_play_capture_success():
    audio_path = face_tts.speak_capture_success()
    if audio_path:
        face_tts.play_audio(audio_path)
    return audio_path

def speak_and_play_welcome():
    audio_path = face_tts.speak_welcome()
    if audio_path:
        face_tts.play_audio(audio_path)
    return audio_path

def cleanup_old_audio_files(keep_days: int = 7):
    return face_tts.cleanup_old_audio_files(keep_days)

def get_audio_stats():
    return face_tts.get_audio_stats()
