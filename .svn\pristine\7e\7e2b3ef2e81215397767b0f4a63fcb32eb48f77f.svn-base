import cv2
import time
import os
import sys
import numpy as np
from .scrfd import SCRFD
import threading
current_dir = os.path.dirname(__file__)
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

project_root = os.path.dirname(os.path.dirname(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)


from utils.path_manager import get_captured_image_path

from api_trigger import trigger_auto_capture

# Import FaceTTS
from face_tts import (
    speak_and_play_no_face, 
    speak_and_play_multiple_faces, 
    speak_and_play_face_too_far,
    speak_and_play_face_direction,
    speak_and_play_keep_face,
    speak_and_play_face_lost_countdown,
    speak_and_play_capture_success,
    speak_and_play_welcome  # Thêm import cho welcome message
)

def should_speak_tts(last_tts_time, tts_cooldown):
    current_time = time.time()
    if current_time - last_tts_time >= tts_cooldown:
        return True, current_time
    return False, last_tts_time

def run_face_capture(save_dir="image", model_path="scrfd_2.5g_bnkps.onnx", frame_source=None, camera_controller=None):
    current_message = ""
    camera_index = 0
    os.makedirs(save_dir, exist_ok=True)
    frame_count = 0
    detect_interval = 3

    detector = SCRFD(model_file=model_path)
    detector.prepare(ctx_id=-1)

    if frame_source is None:
        cap = cv2.VideoCapture(camera_index)
        if not cap.isOpened():
            print("Không mở được webcam")
            return
        use_external_frames = False
    else:
        cap = None
        use_external_frames = True

    if use_external_frames:
        frame_width, frame_height = 640, 480
    else:
        frame_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        frame_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

    center_x, center_y = frame_width // 2, frame_height // 2
    center_tolerance = 180
    offset_tolerance = 100

    min_face_size = 100
    stable_threshold = 10
    face_stable_counter = 0

    countdown_mode = False
    countdown_start_time = None
    countdown_seconds = 0
    saved = False

    delay_between_captures = 10
    last_save_time = 0
    blur_strength = 31  
    
    # Biến kiểm soát TTS
    last_tts_time = 0
    tts_cooldown = 10.0
    
    welcome_played = False
    if camera_controller and camera_controller.session_model.current_session:
        welcome_played = camera_controller.session_model.current_session.get('welcome_voice_played', False)
    
    previous_state = "none"
    previous_direction = ""
    
    # NOTE: mình sẽ không dùng cơ chế stream_paused khi capture để tránh khựng
    # Biến stream (vẫn giữ nếu controller muốn tắt stream)
    pause_sleep = 0.005
    bboxes, landmarks = [], []
    
    while True:
        # nếu controller dừng stream thì chờ (nhưng rất ngắn)
        if camera_controller and not camera_controller.streaming_active_1:
            # không block lâu, chỉ sleep nhỏ
            time.sleep(pause_sleep)
            continue
            
        # nếu controller đặt capture_mode_1 thì tạm dừng stream (nếu bạn dùng tính năng này)
        if camera_controller and camera_controller.capture_mode_1:
            time.sleep(pause_sleep)
            continue
        
        if use_external_frames:
            frame = frame_source()
            if frame is None:
                continue
        else:
            ret, frame = cap.read()
            if not ret:
                break

        display_frame = frame.copy()
        frame_count += 1
        if frame_count % detect_interval == 0:
            bboxes, landmarks = detector.detect(frame, thresh=0.4)

        # --- ROI mask + nền mờ (đã xử lý để bên trong ROI giữ nguyên) ---
        axes_length = (center_tolerance, int(center_tolerance * 1.25))
        # Tạo mask ROI (ellipse trắng)
        roi_mask = np.zeros((frame_height, frame_width), dtype=np.uint8)
        cv2.ellipse(roi_mask, (center_x, center_y), axes_length, 0, 0, 360, 255, -1)

        # Invert mask để lấy vùng ngoài ROI
        mask_outside = cv2.bitwise_not(roi_mask)

        # Tạo hình nền màu
        color_background = np.full_like(display_frame, (200, 240, 255))  # BGR

        # Chỉ giữ ROI nguyên, ngoài ROI tô màu
        display_frame = cv2.bitwise_and(display_frame, display_frame, mask=roi_mask) + \
                        cv2.bitwise_and(color_background, color_background, mask=mask_outside)

        # # Vẽ viền ROI
        # cv2.ellipse(display_frame, (center_x, center_y), axes_length, 0, 0, 360, (0, 255, 255), 2)


        # --- reset saved flag nếu đủ thời gian giữa 2 lần chụp ---
        if saved and time.time() - last_save_time >= delay_between_captures:
            saved = False

        current_state = "none"
        current_direction = ""

        # welcome TTS
        if len(bboxes) > 0 and not welcome_played:
            can_speak, last_tts_time = should_speak_tts(last_tts_time, tts_cooldown)
            if can_speak:
                speak_and_play_welcome()
                welcome_played = True
                if camera_controller and camera_controller.session_model.current_session:
                    camera_controller.session_model.update_session(welcome_voice_played=True)
                print("Đã phát voice welcome khi phát hiện khuôn mặt")

        # --- Lấy các faces trong ROI ---
        faces_in_center = []
        for bbox in bboxes:
            x1, y1, x2, y2, _ = bbox.astype(int)
            w, h = x2 - x1, y2 - y1
            face_cx, face_cy = (x1 + x2) // 2, (y1 + y2) // 2

            if w >= min_face_size and h >= min_face_size and \
               abs(face_cx - center_x) <= offset_tolerance and \
               abs(face_cy - center_y) <= offset_tolerance:
                faces_in_center.append((x1, y1, x2, y2))
                cv2.rectangle(display_frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
                cv2.circle(display_frame, (face_cx, face_cy), 5, (0, 255, 255), -1)

        # --- Xử lý trạng thái dựa trên faces_in_center ---
        if len(faces_in_center) == 0:
            current_state = "no_face_in_center"
            if current_state != previous_state:
                can_speak, last_tts_time = should_speak_tts(last_tts_time, tts_cooldown)
                if can_speak:
                    speak_and_play_no_face()
            saved = False

        else:
            # lấy face đầu tiên
            x1, y1, x2, y2 = faces_in_center[0]
            w, h = x2 - x1, y2 - y1
            face_cx, face_cy = (x1 + x2) // 2, (y1 + y2) // 2

            dx = face_cx - center_x
            dy = face_cy - center_y

            # hướng lệch
            if abs(dx) > offset_tolerance or abs(dy) > offset_tolerance:
                current_state = "direction"
                directions = []
                if abs(dx) > offset_tolerance:
                    directions.append("left" if dx < 0 else "right")
                if abs(dy) > offset_tolerance:
                    directions.append("up" if dy > 0 else "down")

                current_direction = "_".join(sorted(directions))
                current_message = "Please put your face " + " and ".join(directions)

                if current_state != previous_state or current_direction != previous_direction:
                    can_speak, last_tts_time = should_speak_tts(last_tts_time, tts_cooldown)
                    if can_speak and directions:
                        speak_and_play_face_direction(directions[0])

                face_stable_counter = 0
                if countdown_mode:
                    countdown_mode = False
                    countdown_start_time = None
            else:
                # stable counter
                face_stable_counter += 1
                if face_stable_counter >= stable_threshold and not countdown_mode and not saved:
                    current_state = "countdown"
                    countdown_mode = True
                    countdown_start_time = time.time()
                    face_stable_counter = 0
                    current_message = "Keep your face"
                    if current_state != previous_state:
                        can_speak, last_tts_time = should_speak_tts(last_tts_time, tts_cooldown)
                        if can_speak:
                            speak_and_play_keep_face()
                else:
                    current_state = "stable"

        previous_state = current_state
        previous_direction = current_direction

        # --- Countdown và capture: *TRẢ VỀ ẢNH NGAY* KHÔNG BLOCK ---
        if countdown_mode:
            if len(faces_in_center) == 0:
                countdown_mode = False
                countdown_start_time = None
            else:
                elapsed = int(time.time() - countdown_start_time)
                remaining = countdown_seconds - elapsed
                if remaining <= 0:
                    # chụp ảnh (copy 1 lần)
                    captured_image = frame.copy()
                    saved = True
                    last_save_time = time.time()
                    countdown_mode = False
                    print("✅ Đã chụp ảnh (non-blocking)")

                    # lưu và gọi API trong thread (non-blocking)
                    def save_and_trigger(image):
                        try:
                            triggered = trigger_auto_capture()
                        except Exception as e:
                            triggered = False
                            print("trigger_auto_capture lỗi:", e)

                        if triggered:
                            print("📡 Auto capture triggered via API")
                        else:
                            try:
                                timestamp = int(time.time())
                                filename = get_captured_image_path('face', timestamp)
                                cv2.imwrite(filename, image)
                                print(f"💾 Đã lưu ảnh: {filename}")
                            except Exception as e:
                                print("Lưu ảnh lỗi:", e)

                    threading.Thread(target=save_and_trigger, args=(captured_image,), daemon=True).start()

                    # phát TTS thành công (non-blocking)
                    can_speak, last_tts_time = should_speak_tts(last_tts_time, tts_cooldown)
                    if can_speak:
                        speak_and_play_capture_success()

                    # ==== TRẢ VỀ ẢNH NGAY LẬP TỨC ====
                    # encode ảnh vừa chụp và yield nó một lần ngay bây giờ
                    encode_param = [int(cv2.IMWRITE_JPEG_QUALITY), 80]
                    ret, buffer = cv2.imencode('.jpg', captured_image, encode_param)
                    if ret:
                        frame_bytes = buffer.tobytes()
                        yield (b'--frame\r\n'
                               b'Content-Type: image/jpeg\r\n\r\n' + frame_bytes + b'\r\n')
                    # sau khi yield ảnh chụp, tiếp tục vòng lặp bình thường (không pause)

        # Nếu đã có ảnh vừa chụp và chưa trả về (nếu bạn muốn luôn show captured image,
        # logic ở trên đã trả về ngay rồi) — tiếp tục mã bình thường.

        # Cuối vòng lặp: encode display_frame (stream bình thường)
        encode_param = [int(cv2.IMWRITE_JPEG_QUALITY), 80]
        ret, buffer = cv2.imencode('.jpg', display_frame, encode_param)
        if not ret:
            # fallback: encode raw frame nếu lỗi
            ret, buffer = cv2.imencode('.jpg', frame, encode_param)
        frame_bytes = buffer.tobytes()
        yield (b'--frame\r\n'
               b'Content-Type: image/jpeg\r\n\r\n' + frame_bytes + b'\r\n')
