# card_stream.py
import cv2
import numpy as np
import os
import sys
import time

sys.path.append(os.path.dirname(os.path.dirname(__file__)))
from utils.path_manager import path_manager

try:
    from controllers.camera_controller import camera_controller
except ImportError:
    camera_controller = None  

def detect_card_stream(frame_source=None, roi_rect=(100, 120, 500, 300)):
    """
    roi_rect: (x, y, w, h) nếu muốn chỉ detect trong 1 vùng hình chữ nhật.
              Nếu None -> giữ nguyên hành vi detect to<PERSON>n khung hình.
    """
    cam_index = 0
    border_ratio = 0.10
    area_min_ratio = 0.003
    stable_frames = 30
    maha_thresh_d2 = 11.34
    weight_l, weight_a, weight_b = 0.4, 1.0, 1.0
    morph_close, morph_open = 5, 3
    edge_density_min = 0.002
    return_crop = False
    rearm_absence_frames = 200
    jpeg_quality = 85

    def clamp_roi(x, y, w, h, W, H):
        x = max(0, min(x, W - 1))
        y = max(0, min(y, H - 1))
        w = max(1, min(w, W - x))
        h = max(1, min(h, H - y))
        return x, y, w, h

    def sample_bg_stats(lab):
        H, W = lab.shape[:2]
        t = int(max(2, border_ratio * H))
        l = int(max(2, border_ratio * W))
        top, bottom = lab[:t, :], lab[H-t:, :]
        left, right = lab[:, :l], lab[:, W-l:]
        ring = np.concatenate([top.reshape(-1,3), bottom.reshape(-1,3),
                               left.reshape(-1,3), right.reshape(-1,3)], axis=0).astype(np.float32)
        ring[:,0] *= weight_l; ring[:,1] *= weight_a; ring[:,2] *= weight_b
        mu = ring.mean(axis=0)
        X  = ring - mu
        cov = (X.T @ X) / max(1, len(ring)-1)
        cov += np.eye(3, dtype=np.float32) * 1e-3
        return mu, np.linalg.inv(cov)

    def mahalanobis_d2(lab, mu, inv_cov):
        labf = lab.astype(np.float32).reshape(-1,3)
        labf[:,0] *= weight_l; labf[:,1] *= weight_a; labf[:,2] *= weight_b
        diff = labf - mu
        return np.einsum('ij,jk,ik->i', diff, inv_cov, diff).reshape(lab.shape[:2])

    def edge_density(cnt, gray):
        x,y,w,h = cv2.boundingRect(cnt)
        if w*h == 0: return 0.0
        roi = gray[y:y+h, x:x+w]
        edges = cv2.Canny(roi, 60, 180)
        return float(np.count_nonzero(edges)) / (w*h)

    def bgr_to_mjpeg(img_bgr):
        ok, buf = cv2.imencode('.jpg', img_bgr, [int(cv2.IMWRITE_JPEG_QUALITY), jpeg_quality])
        if not ok: return None
        return (b'--frame\r\n'
                b'Content-Type: image/jpeg\r\n\r\n' +
                buf.tobytes() + b'\r\n')

    if frame_source is None:
        cap = cv2.VideoCapture(cam_index)
        if not cap.isOpened():
            yield bgr_to_mjpeg(np.zeros((240,320,3), np.uint8))
            return
        use_external_frames = False
    else:
        cap = None
        use_external_frames = True

    stable = 0
    armed = True
    absence_cnt = 0
    saved_count = 0

    saved = False
    captured_image = None
    last_save_time = 0
    delay_between_captures = 10

    frame_count = 0
    start_time = time.time()
    try:
        while True:
            frame_count += 1
            if use_external_frames:
                frame = frame_source()
                if frame is None:
                    continue
            else:
                ok, frame = cap.read()
                if not ok: break

            H, W = frame.shape[:2]
            view = frame.copy()

            if roi_rect is None:
                rx, ry, rw, rh = 0, 0, W, H
            else:
                rx, ry, rw, rh = clamp_roi(*roi_rect, W=W, H=H)

            # if roi_rect is not None:
            #     cv2.rectangle(view, (rx, ry), (rx+rw, ry+rh), (255, 255, 0), 2)

            frame_roi = frame[ry:ry+rh, rx:rx+rw]
            blur = cv2.GaussianBlur(frame_roi, (5,5), 0)
            lab  = cv2.cvtColor(blur, cv2.COLOR_BGR2LAB)

            mu, inv_cov = sample_bg_stats(lab)
            mask = (mahalanobis_d2(lab, mu, inv_cov) > maha_thresh_d2).astype(np.uint8) * 255

            if morph_close >= 3:
                mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, np.ones((morph_close,morph_close), np.uint8))
            if morph_open >= 3:
                mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, np.ones((morph_open,morph_open), np.uint8))

            cnts, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            best_cnt, best_area = None, 0
            roi_area = rw * rh  

            for c in cnts:
                a = cv2.contourArea(c)
                if a > best_area and a >= area_min_ratio * roi_area:
                    best_area, best_cnt = a, c

            card_detected = False

            gray_roi = cv2.cvtColor(frame_roi, cv2.COLOR_BGR2GRAY)

            if best_cnt is not None:
                x,y,w,h = cv2.boundingRect(best_cnt)
                ed = edge_density(best_cnt, gray_roi)
                if ed >= edge_density_min:
                    stable += 1
                    card_detected = True
                else:
                    stable = 0

                # cv2.rectangle(view, (rx+x, ry+y), (rx+x+w, ry+y+h), (0,255,0), 2)
            else:
                stable = 0

            if card_detected:
                absence_cnt = 0
            else:
                absence_cnt += 1
                if absence_cnt >= rearm_absence_frames:
                    armed = True

            if armed and card_detected and stable >= stable_frames and not saved:
                if return_crop and best_cnt is not None:
                    captured = frame[ry+y:ry+y+h, rx+x:rx+x+w]
                else:
                    captured = frame

                timestamp = int(time.time())
                filepath = path_manager.get_captured_image_path('card', timestamp)
                cv2.imwrite(filepath, captured)

                captured_image = captured.copy()
                saved = True
                last_save_time = time.time()
                saved_count += 1
                armed = False
                stable = 0
                absence_cnt = 0

                print(f"Card captured and saved: {filepath}")

                try:
                    if camera_controller:
                        if not camera_controller.session_model.current_session:
                            session_id = camera_controller.session_model.create_session()
                            print(f" Created new session for card capture: {session_id}")

                        camera_controller.session_model.save_captured_image(filepath, 'card')
                        print(f"Card saved to session: {filepath}")

                        camera_controller.check_and_trigger_ai_pipeline()
                    else:
                        print("Camera controller not available - skipping session save")
                except Exception as e:
                    print(f"Error saving card to session: {e}")

            if saved and captured_image is not None:
                yield bgr_to_mjpeg(captured_image)
            else:
                yield bgr_to_mjpeg(view)

    except GeneratorExit:
        pass
    finally:
        if cap is not None:
            cap.release()


# def _decode_mjpeg_chunk(chunk: bytes):

#     if chunk is None:
#         return None
#     try:
#         parts = chunk.split(b"\r\n\r\n", 1)
#         if len(parts) < 2:
#             return None
#         jpeg_and_tail = parts[1]
#         if jpeg_and_tail.endswith(b"\r\n"):
#             jpeg_bytes = jpeg_and_tail[:-2]
#         else:
#             jpeg_bytes = jpeg_and_tail

#         arr = np.frombuffer(jpeg_bytes, dtype=np.uint8)
#         img = cv2.imdecode(arr, cv2.IMREAD_COLOR)
#         return img
#     except Exception:
#         return None

# if __name__ == "__main__":
#     print("Press Q to quit.")
#     stream = detect_card_stream()
#     for chunk in stream:
#         frame = _decode_mjpeg_chunk(chunk)
#         if frame is None:
#             continue
#         cv2.imshow("Card Stream Test", frame)
#         key = cv2.waitKey(1) & 0xFF
#         if key == ord('q'):
#             break
#     cv2.destroyAllWindows()
