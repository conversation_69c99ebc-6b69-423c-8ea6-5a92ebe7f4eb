/* ========================================
   CONTROLS STYLES CSS - Button styling and form controls
   Styles for buttons, dropdowns, and interactive controls
   Includes Apple Design System button and form styles
   ======================================== */

/* ===== APPLE BUTTONS (Vibrant style) ===== */
.apple-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-md);
  padding: 20px 40px;
  font-size: 18px;
  font-weight: var(--font-weight-bold);
  border: none;
  border-radius: var(--radius-2xl);
  cursor: pointer;
  transition: var(--transition);
  text-decoration: none;
  min-height: 64px;
  position: relative;
  overflow: hidden;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
  font-family: var(--font-family);
  margin: var(--spacing-sm);
}

.apple-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  transition: left 0.5s;
}

.apple-button:hover::before {
  left: 100%;
}

.apple-button-primary {
  background: var(--primary-gradient);
  color: white;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.apple-button-primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 35px rgba(102, 126, 234, 0.6);
}

.apple-button-secondary {
  background: var(--secondary-gradient);
  color: white;
  box-shadow: 0 8px 25px rgba(240, 147, 251, 0.4);
}

.apple-button-secondary:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 35px rgba(240, 147, 251, 0.6);
}

/* ========================================
   CONTROLS SECTION STYLES
   ======================================== */

/* Controls grid - Lưới điều khiển */
.controls-grid {
    display: grid;                               /* CSS Grid */
    grid-template-columns: 1fr 1fr;             /* 2 cột bằng nhau */
    gap: var(--spacing-xl);                      /* Khoảng cách giữa các control */
    align-items: start;                          /* Căn đầu theo chiều dọc */
}

/* Control group - Nhóm điều khiển */
.control-group {
    background: rgba(255, 255, 255, 0.1);       /* Background trong suốt */
    border-radius: var(--radius-lg);             /* Bo góc */
    padding: var(--spacing-lg);                  /* Padding bên trong */
    border: 1px solid rgba(255, 255, 255, 0.2); /* Viền trắng trong suốt */
}

/* Control title - Tiêu đề điều khiển */
.control-title {
    color: var(--color-text);                    /* Màu chữ */
    font-size: 18px;                            /* Kích thước font */
    font-weight: var(--font-weight-semibold);   /* Độ đậm font */
    margin-bottom: var(--spacing-md);           /* Khoảng cách dưới */
    display: flex;                               /* Flexbox */
    align-items: center;                         /* Căn giữa theo chiều dọc */
    gap: var(--spacing-sm);                      /* Khoảng cách giữa icon và text */
}

/* ========================================
   COMBINED CONTROLS STYLES
   ======================================== */

/* Center action container - Container cho nút hành động ở giữa */
.center-action-container {
    display: flex;                               /* Flexbox */
    justify-content: center;                     /* Căn giữa theo chiều ngang */
    align-items: center;                         /* Căn giữa theo chiều dọc */
    width: 100%;                                 /* Chiều rộng 100% */
}

/* Main action button enhanced styling */
.main-action-button {
    font-size: 20px !important;                 /* Kích thước font lớn hơn */
    padding: 24px 48px !important;              /* Padding lớn hơn */
    min-height: 72px !important;                /* Chiều cao tối thiểu lớn hơn */
    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.5) !important; /* Shadow đậm hơn */
}

/* ========================================
   AUTO-RESET TIMER STYLES - DISABLED
   ======================================== */

/* COMMENTED OUT: Auto-reset timer styles disabled */
#autoResetTimer {
    animation: fadeInUp 0.5s ease-out;
}

#autoResetTimer > div {
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

#autoResetTimer > div:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(67, 233, 123, 0.3);
}

/* Countdown seconds styling */
#countdownSeconds {
    font-weight: var(--font-weight-bold);       /* Bold countdown */
    font-size: 1.1em;                          /* Slightly larger */
    color: var(--color-success);               /* Success color */
}

/* Fade in up animation */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Combined controls - Optimized for single centered button */
.combined-controls {
    background: linear-gradient(135deg,
        rgba(102, 126, 234, 0.1) 0%,
        rgba(118, 75, 162, 0.08) 100%
    );                                          /* Gradient background */
    border-radius: var(--radius-lg);            /* Rounded corners */
    padding: var(--spacing-lg);                 /* Comfortable padding */
    border: 1px solid rgba(102, 126, 234, 0.2); /* Subtle border */
    margin-top: var(--spacing-md);              /* Spacing from camera */
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.15); /* Elegant shadow */
}

/* Controls grid improvements - MAXIMUM COMPACT for camera space */
.combined-controls .controls-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;             /* 2 cột bằng nhau hoàn toàn */
    gap: 4px !important;                         /* ULTRA MINIMAL gap */
    align-items: stretch;                        /* Căn đều chiều cao */
    margin-bottom: 0;                           /* Không margin bottom */
    align-content: center;                       /* Căn giữa nội dung */
    min-height: 50px !important;                /* MAXIMUM COMPACT height */
}

/* Control group improvements - MAXIMUM COMPACT for camera space */
.combined-controls .control-group {
    margin: 0;
    padding: 4px !important;                     /* ULTRA MINIMAL padding */
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.08) 0%,
        rgba(255, 255, 255, 0.05) 100%
    );                                          /* Gradient background */
    border-radius: 3px !important;              /* ULTRA MINIMAL border radius */
    border: 1px solid rgba(102, 126, 234, 0.2); /* Viền tím nhẹ */
    display: flex;
    flex-direction: column;
    justify-content: center;                     /* Căn giữa nội dung */
    align-items: stretch;                        /* Căn đều chiều rộng */
    min-height: 50px !important;                /* MAXIMUM COMPACT height */
    height: 100%;                               /* Chiều cao 100% để đồng đều */
    box-shadow: 0 1px 8px rgba(102, 126, 234, 0.08) !important; /* LIGHTER shadow */
}

.combined-controls .control-title {
    margin-bottom: var(--spacing-xs);           /* REDUCED margin */
    font-size: 14px;                            /* SMALLER font size */
    font-weight: var(--font-weight-semibold);
    text-align: center;                          /* Căn giữa title */
    display: flex;                               /* Flexbox để căn icon */
    align-items: center;                         /* Căn giữa icon và text */
    justify-content: center;                     /* Căn giữa nội dung */
    gap: var(--spacing-xs);                      /* Khoảng cách giữa icon và text */
}

.combined-controls .control-title span {
    font-size: 16px;                            /* SMALLER icon size */
    line-height: 1;                             /* Line height chuẩn */
}

/* ========================================
   BUTTON & SELECT STYLING IMPROVEMENTS
   ======================================== */

/* Apple select styling - Cải thiện select box (BEAUTIFUL DESIGN) */
.combined-controls .apple-select {
    width: 100% !important;
    font-size: 15px !important;                 /* Font size rõ ràng */
    padding: 14px 18px !important;              /* Padding thoải mái */
    border: 2px solid rgba(102, 126, 234, 0.4) !important; /* Viền tím */
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.95) 0%,
        rgba(248, 250, 252, 0.95) 100%
    ) !important;                               /* Gradient background */
    border-radius: var(--radius-md) !important;
    min-height: 48px !important;                /* Chiều cao thoải mái */
    box-sizing: border-box !important;          /* Đảm bảo tính toán kích thước chính xác */
    box-shadow: 0 2px 10px rgba(102, 126, 234, 0.1) !important; /* Shadow nhẹ */
}

/* Apple button styling - Cải thiện button (STUNNING DESIGN) */
.combined-controls .apple-button {
    width: 100% !important;
    font-size: 15px !important;                 /* Font size rõ ràng */
    padding: 14px 18px !important;              /* Padding thoải mái */
    border-radius: var(--radius-md) !important;
    min-height: 48px !important;                /* Chiều cao thoải mái */
    box-sizing: border-box !important;          /* Đảm bảo tính toán kích thước chính xác */
    display: flex !important;                   /* Flexbox để căn giữa nội dung */
    align-items: center !important;             /* Căn giữa theo chiều dọc */
    justify-content: center !important;         /* Căn giữa theo chiều ngang */
    background: linear-gradient(135deg,
        #667eea 0%,
        #764ba2 100%
    ) !important;                               /* Gradient tím đẹp */
    border: none !important;                    /* Không viền */
    color: white !important;                    /* Chữ trắng */
    font-weight: 600 !important;               /* Font weight đậm */
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3) !important; /* Shadow đẹp */
    transition: all 0.3s ease !important;      /* Transition mượt */
}

/* Button hover effects - Hiệu ứng hover cho button */
.combined-controls .apple-button:hover {
    transform: translateY(-2px) !important;     /* Nâng lên nhẹ */
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4) !important; /* Shadow đậm hơn */
    background: linear-gradient(135deg,
        #7c8cff 0%,
        #8a5fb8 100%
    ) !important;                               /* Gradient sáng hơn khi hover */
}

/* Select hover effects - Hiệu ứng hover cho select */
.combined-controls .apple-select:hover {
    border-color: rgba(102, 126, 234, 0.6) !important; /* Viền đậm hơn */
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2) !important; /* Shadow đậm hơn */
}

/* Button group container - ADJACENT BUTTON LAYOUT */
.button-group-container {
    display: flex !important;                  /* Flexbox layout */
    gap: var(--spacing-sm) !important;         /* Gap between buttons */
    align-items: center !important;            /* Center alignment */
    justify-content: center !important;        /* Center the group */
    flex-wrap: wrap !important;                /* Wrap on small screens */
}

/* Main action button - FULL SIZE */
.main-action-button {
    flex: 1 !important;                        /* Take available space */
    min-width: 200px !important;               /* Minimum width */
}

/* Adjacent retake button - COMPACT SIZE */
.retake-button-adjacent {
    font-size: 14px !important;                /* Smaller font size */
    padding: 10px 16px !important;             /* Compact padding */
    min-height: 44px !important;               /* Match main button height */
    min-width: 120px !important;               /* Minimum width */
    max-width: 150px !important;               /* Maximum width constraint */
    flex-shrink: 0 !important;                 /* Don't shrink */
}

/* ========================================
   APPLE FORM ELEMENTS
   ======================================== */

/* ===== VIBRANT FORM ELEMENTS ===== */
.apple-select {
  width: 100%;
  min-width: 250px;
  max-width: 400px;
  padding: 16px 20px;
  font-size: 16px;
  font-family: var(--font-family);
  font-weight: var(--font-weight-medium);
  border: 2px solid rgba(255,255,255,0.4);
  border-radius: var(--radius-lg);
  background: var(--bg-card);
  color: var(--color-text);
  transition: var(--transition);
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23667eea' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 16px center;
  background-repeat: no-repeat;
  background-size: 20px;
  padding-right: 50px;
  backdrop-filter: blur(20px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
  cursor: pointer;
}

.apple-select:focus {
  outline: none;
  border-color: rgba(67, 233, 123, 0.8);
  box-shadow: 0 0 0 4px rgba(67, 233, 123, 0.3);
  transform: translateY(-2px);
}

.apple-select:hover {
  border-color: rgba(255,255,255,0.6);
  transform: translateY(-1px);
  box-shadow: 0 12px 35px rgba(102, 126, 234, 0.3);
}

.apple-select option {
  padding: 12px 16px;
  font-size: 16px;
  font-weight: var(--font-weight-medium);
  background: #ffffff;
  color: #333333;
  border: none;
}

.apple-select option:hover {
  background: #f0f0f0;
}

.apple-select option:checked {
  background: #007AFF;
  color: white;
}

/* ========================================
   STANDARD FORM CONTROLS
   ======================================== */

/* Input field styling */
.form-input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-md);
    background: rgba(255, 255, 255, 0.1);
    color: var(--color-text);
    font-size: 16px;
    transition: all 0.3s ease;
}

.form-input:focus {
    outline: none;
    border-color: var(--color-primary);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Checkbox styling */
.form-checkbox {
    appearance: none;
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.1);
    cursor: pointer;
    position: relative;
    transition: all 0.3s ease;
}

.form-checkbox:checked {
    background: var(--color-primary);
    border-color: var(--color-primary);
}

.form-checkbox:checked::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 14px;
    font-weight: bold;
}

/* Radio button styling */
.form-radio {
    appearance: none;
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    cursor: pointer;
    position: relative;
    transition: all 0.3s ease;
}

.form-radio:checked {
    border-color: var(--color-primary);
}

.form-radio:checked::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: var(--color-primary);
}

/* Toggle switch styling */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.2);
    transition: 0.3s;
    border-radius: 24px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background: white;
    transition: 0.3s;
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background: var(--color-primary);
}

input:checked + .toggle-slider:before {
    transform: translateX(26px);
}
