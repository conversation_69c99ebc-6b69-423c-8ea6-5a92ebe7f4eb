/* ========================================
   API CLIENT.JS - Backend API calls and data fetching
   Handles all API communications, data fetching, and error handling
   ======================================== */

// ========================================
// DATA LOADING FUNCTIONS
// ========================================

// Load and display result data - Tải và hiển thị dữ liệu kết quả
function loadAndDisplayResults() {
    console.log('🔄 Loading result data...');

    // Gọi API để lấy trạng thái session
    fetch('/api/session_status')
        .then(response => {
            console.log('📡 Session status response:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('📊 Session data received:', data);

            // Kiểm tra nếu có dữ liệu session
            if (data.status === 'success' && data.session) {
                // Lấy thông tin card và ảnh đã tạo
                const cardInfo = data.session.card_info || {};
                const generatedImages = data.session.generated_images || [];

                console.log('📄 Card info:', cardInfo);
                console.log('🖼️ Generated images count:', generatedImages.length);

                // Hiển thị dữ liệu lên UI
                if (typeof populateCardInfo === 'function') {
                    populateCardInfo(cardInfo);
                }
                if (typeof populateGeneratedImages === 'function') {
                    populateGeneratedImages(generatedImages);
                }

                // Cập nhật tiến trình workflow dựa trên dữ liệu có sẵn
                if (generatedImages.length > 0) {
                    if (typeof updateWorkflowProgress === 'function') {
                        updateWorkflowProgress(3, 'complete'); // Đã có ảnh AI
                    }
                } else if (Object.keys(cardInfo).length > 0) {
                    if (typeof updateWorkflowProgress === 'function') {
                        updateWorkflowProgress(2, 'complete'); // Đã có thông tin card
                    }
                }
            } else {
                console.log('⚠️ No session data available:', data.message || 'Unknown reason');
            }
        })
        .catch(error => {
            console.error('❌ Error loading result data:', error);
        });
}

// Force refresh results - Buộc làm mới kết quả
function forceRefreshResults() {
    console.log('🔄 Force refreshing results...');

    // Gọi API nhiều lần để đảm bảo lấy được dữ liệu
    let attempts = 0;
    const maxAttempts = 5;

    const refreshInterval = setInterval(() => {
        attempts++;
        console.log(`🔄 Refresh attempt ${attempts}/${maxAttempts}`);

        loadAndDisplayResults();

        if (attempts >= maxAttempts) {
            clearInterval(refreshInterval);
            console.log('✅ Force refresh completed');
        }
    }, 2000); // Mỗi 2 giây
}

// ========================================
// BACKEND API INTEGRATION FUNCTIONS
// ========================================

// Fetch OCR results from backend with retry logic
function fetchOCRResults(retryCount = 0, maxRetries = 10, skipAudio = false) {
    console.log(`📡 Fetching OCR results from backend... (attempt ${retryCount + 1}/${maxRetries + 1})`);

    return fetch('/api/ocr_results')
        .then(response => {
            console.log(`📡 OCR API response status: ${response.status}`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log(`📄 OCR results received (attempt ${retryCount + 1}):`, data);

            if (data.status === 'success' && data.ocr_data) {
                console.log('✅ OCR data received successfully:', data.ocr_data);

                // Kiểm tra chất lượng OCR data
                const quality = data.quality || {};
                console.log(`📊 OCR Quality: ${quality.fields_extracted || 0}/${quality.total_fields || 0} fields (${quality.percentage || 0}%)`);

                // 1) Update UI with OCR and show CardInfo immediately
                if (typeof updateCardInfoDisplay === 'function') {
                    updateCardInfoDisplay(data.ocr_data);
                }
                if (typeof showSectionWithAnimation === 'function') {
                    showSectionWithAnimation('resultsSection', 'fade-in');
                    showSectionWithAnimation('cardInfoSection', 'slide-in');
                }

                // 3) Play TTS if already available, otherwise poll until it is
                const tryPlayAudio = (audioUrl) => {
                    console.log('🎵 Audio file URL received:', audioUrl);
                    if (!skipAudio && typeof playTTSAudio === 'function') {
                        playTTSAudio(audioUrl);
                    } else if (skipAudio) {
                        console.log('🔇 Audio playback skipped as requested');
                    }
                };

                if (data.audio_file) {
                    tryPlayAudio(data.audio_file);
                } else {
                    console.log('⌛ Audio not ready yet. Will poll for TTS while showing Card Info...');
                    let audioAttempts = 0;
                    const maxAudioAttempts = 15; // ~20-30s total depending on interval
                    const intervalMs = 2000;
                    const audioInterval = setInterval(() => {
                        audioAttempts++;
                        fetch('/api/ocr_results')
                            .then(r => r.ok ? r.json() : Promise.reject(new Error('Audio poll HTTP ' + r.status)))
                            .then(rdata => {
                                if (rdata && rdata.audio_file) {
                                    clearInterval(audioInterval);
                                    tryPlayAudio(rdata.audio_file);
                                }
                            })
                            .catch(err => console.warn('⚠️ Audio poll error:', err));
                        if (audioAttempts >= maxAudioAttempts) {
                            clearInterval(audioInterval);
                            console.log('⏹️ Stop polling for audio (timeout)');
                        }
                    }, intervalMs);
                }

                return data.ocr_data;

            } else if (data.status === 'error' && retryCount < maxRetries) {
                // If no data available and we have retries left, wait and retry
                console.log(`⏳ OCR data not ready, retrying in 2 seconds... (${retryCount + 1}/${maxRetries})`);
                console.log(`🔍 Debug info:`, data.debug || 'No debug info');

                return new Promise(resolve => {
                    setTimeout(() => {
                        resolve(fetchOCRResults(retryCount + 1, maxRetries, skipAudio));
                    }, 2000);
                });
            } else {
                // Final attempt failed
                console.error('❌ OCR fetch failed after all retries:', data);
                throw new Error(data.message || 'OCR data not available after retries');
            }
        })
        
}

// Fetch generated images from backend with retry logic
function fetchGeneratedImages(retryCount = 0, maxRetries = 5) {
    console.log(`📡 Fetching generated images from backend... (attempt ${retryCount + 1}/${maxRetries + 1})`);

    return fetch('/api/generated_images')
        .then(response => {
            console.log(`📡 Images API response status: ${response.status}`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('🖼️ Generated images received:', data);
            if (data.status === 'success' && data.images && data.images.length > 0) {
                if (typeof updateGeneratedImagesDisplay === 'function') {
                    updateGeneratedImagesDisplay(data);
                }
                // Ensure images section is visible (no auto-scroll)
                if (typeof showSectionWithAnimation === 'function') {
                    showSectionWithAnimation('resultsSection', 'fade-in');
                    showSectionWithAnimation('imagesSection', 'slide-in');
                }
                return data.images;
            } else if (data.status === 'error' && data.message && retryCount < maxRetries) {
                // If no images available and we have retries left, wait and retry
                console.log(`⏳ Generated images not ready, retrying in 3 seconds... (${retryCount + 1}/${maxRetries})`);
                return new Promise(resolve => {
                    setTimeout(() => {
                        resolve(fetchGeneratedImages(retryCount + 1, maxRetries));
                    }, 3000);
                });
            } else {
                throw new Error(data.message || 'Generated images not available');
            }
        })
        .catch(error => {
            console.error('❌ Error fetching generated images:', error);
            if (retryCount < maxRetries) {
                console.log(`⏳ Retrying images fetch in 3 seconds... (${retryCount + 1}/${maxRetries})`);
                return new Promise(resolve => {
                    setTimeout(() => {
                        resolve(fetchGeneratedImages(retryCount + 1, maxRetries));
                    }, 3000);
                });
            } else {
                if (typeof showMessage === 'function') {
                    showMessage('⚠️ Không thể lấy ảnh đã tạo sau nhiều lần thử. Vui lòng thử lại.', 'error');
                }
                return null;
            }
        });
}

// ========================================
// COMBINED WORKFLOW API CALLS
// ========================================

// Combined capture and generate - NEW UNIFIED WORKFLOW
function combinedCaptureAndGenerate() {
    console.log('🚀 Starting Combined Capture & Generate Workflow...');

    // Reset all sections trước khi bắt đầu
    if (typeof resetAllSections === 'function') {
        resetAllSections();
    }

    // Vô hiệu hóa nút để tránh click nhiều lần
    const combinedBtn = document.getElementById('combinedActionBtn');
    if (combinedBtn) {
        combinedBtn.disabled = true;
        combinedBtn.innerHTML = '<span>⏳</span> Đang xử lý...';
    }

    // Hiển thị progress bar
    if (typeof showProgressBar === 'function') {
        showProgressBar();
        if (typeof updateProgressBar === 'function') {
            updateProgressBar(10);
        }
    }

    // Bắt đầu progressive disclosure workflow
    if (typeof startProgressiveDisclosure === 'function') {
        startProgressiveDisclosure();
    }

    // Cho phép scroll khi bắt đầu workflow
    if (typeof setPageScrolling === 'function') {
        setPageScrolling(true);
    }

    // UNIFIED API CALL - Sử dụng prompt cố định từ prompt.txt
    console.log('🚀 Calling unified capture & generate API...');
    if (typeof updateProgressBar === 'function') {
        updateProgressBar(20);
    }

    // Start early OCR polling so Card Info shows as soon as OCR finishes
                    if (typeof fetchOCRResults === 'function') {
                    setTimeout(() => fetchOCRResults(0, 10, false), 300); // begin polling almost immediately
    }

    // Call the combined API endpoint (không cần gửi prompt_template)
    fetch('/api/combined_capture_generate', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({})
    })
    .then(response => {
        console.log('📡 API Response received:', response.status);
        if (typeof updateProgressBar === 'function') {
            updateProgressBar(50);
        }
        return response.json();
    })
    .then(data => {
        console.log('📊 API Response data:', data);
        console.log('🔍 Response keys:', Object.keys(data));
        console.log('🔍 Status:', data.status);
        console.log('🔍 Has card_info:', !!data.card_info);
        console.log('🔍 Has generated_images:', !!data.generated_images);
        console.log('🔍 Generated images count:', data.generated_images ? data.generated_images.length : 0);
        console.log('🔍 Card info content:', data.card_info);
        console.log('🔍 Generated images content:', data.generated_images);

        if (typeof updateProgressBar === 'function') {
            updateProgressBar(80);
        }

        if (data.status === 'success') {
            console.log('✅ Combined workflow completed successfully!');

            // Update progress
            if (typeof updateProgressBar === 'function') {
                updateProgressBar(100);
            }

            // Display results with error handling
            try {
                console.log('🎯 About to call displayWorkflowResults...');
                console.log('🎯 displayWorkflowResults function exists:', typeof displayWorkflowResults);
                console.log('🎯 Data to pass:', data);

                if (typeof displayWorkflowResults === 'function') {
                    displayWorkflowResults(data);
                    console.log('✅ Results displayed successfully');
                } else {
                    console.error('❌ displayWorkflowResults function not found!');
                }
            } catch (displayError) {
                console.error('❌ Error displaying results:', displayError);
                console.error('❌ Error stack:', displayError.stack);
                if (typeof showMessage === 'function') {
                    showMessage('⚠️ Workflow hoàn thành nhưng có lỗi hiển thị. Vui lòng refresh trang.', 'warning');
                }
            }

            // Auto-reset will be triggered by displayWorkflowResults function

        } else if (data.status === 'partial_success') {
            console.log('⚠️ Partial success: OCR completed, AI generation failed');

            // Update progress
            if (typeof updateProgressBar === 'function') {
                updateProgressBar(70);
            }

            // Display OCR results
            if (data.card_info && typeof populateCardInfo === 'function') {
                populateCardInfo(data.card_info);
                if (typeof showCardInfoSection === 'function') {
                    showCardInfoSection();
                }
            }

            // Show retry button for AI generation
            const retryBtn = document.getElementById('retryAIBtn');
            if (retryBtn) {
                retryBtn.style.display = 'inline-block';
            }

            // Show partial success message
            if (typeof showMessage === 'function') {
                showMessage('⚠️ OCR hoàn thành. Tạo ảnh AI thất bại - có thể thử lại.', 'warning');
            }

        } else if (data.status === 'auto_reset') {
            console.log('🔄 OCR Quality Check Failed - Auto-resetting UI...');
            console.log(`   Empty fields: ${data.empty_fields || []}`);
            console.log(`   Empty count: ${data.empty_count || 0}/8`);
            
            // Hiển thị thông báo cho user
            if (typeof showMessage === 'function') {
                showMessage(`⚠️ Chất lượng OCR kém (${data.empty_count}/8 trường trống). Đang reset để chụp lại...`, 'warning');
            }
            
            // Auto-reset UI sau 2 giây
            setTimeout(() => {
                console.log('🔄 Starting auto-reset UI...');
                autoResetUI();
            }, 2000);
            
        } else {
            console.error('❌ Combined workflow failed:', data.message);
            if (typeof showMessage === 'function') {
                showMessage(`❌ Lỗi: ${data.message}`, 'error');
            }
        }
    })
    .catch(error => {
        console.error('❌ API Error:', error);
        if (typeof showMessage === 'function') {
            showMessage('❌ Lỗi kết nối API. Vui lòng thử lại.', 'error');
        }
    })
    .finally(() => {
        // Reset UI regardless of success/failure
        setTimeout(() => {
            if (typeof hideProgressBar === 'function') {
                hideProgressBar();
            }

            if (combinedBtn) {
                combinedBtn.disabled = false;
                combinedBtn.innerHTML = '<span>⚡</span> Chụp ảnh & Tạo AI';
            }

            // Show retake button
            const retakeBtn = document.getElementById('retakeBtn');
            if (retakeBtn) {
                retakeBtn.style.display = 'inline-block';
            }
        }, 1000);
    });
}

// Auto-reset UI function for OCR quality check failure
function autoResetUI() {
    console.log('🔄 Auto-resetting UI due to poor OCR quality...');
    
    // 1. Clear session UI
    if (typeof clearSessionUI === 'function') {
        clearSessionUI();
    }
    
    // 2. Reset camera monitors
    if (window.cardCameraMonitor) {
        window.cardCameraMonitor.reset();
    }
    if (window.faceCameraMonitor) {
        window.faceCameraMonitor.reset();
    }
    
    // 3. Reset capture state
    if (typeof retakeAll === 'function') {
        retakeAll();
    }
    
    // 4. Reset workflow state
    if (typeof resetWorkflowState === 'function') {
        resetWorkflowState();
    }
    
    // 5. Reset all sections
    if (typeof resetAllSections === 'function') {
        resetAllSections();
    }
    
    // 6. Show success message
    if (typeof showMessage === 'function') {
        showMessage('✅ Đã reset hoàn toàn. Sẵn sàng chụp lại với chất lượng tốt hơn!', 'success');
    }
    
    console.log('✅ Auto-reset UI completed');
}

// Export functions to global scope
window.loadAndDisplayResults = loadAndDisplayResults;
window.forceRefreshResults = forceRefreshResults;
window.fetchOCRResults = fetchOCRResults;
window.fetchGeneratedImages = fetchGeneratedImages;
window.combinedCaptureAndGenerate = combinedCaptureAndGenerate;
window.autoResetUI = autoResetUI;
