import json
from typing import Dict, Optional

from database.crud import CustomerCRUD, CheckinCRUD, CustomerHistoryCRUD

class DatabaseService:

    def __init__(self):
        self.customer_crud = CustomerCRUD()
        self.checkin_crud = CheckinCRUD()
        self.history_crud = CustomerHistoryCRUD()
    
    def save_card_info_to_db(self, card_info: Dict, session_data: Optional[Dict] = None, force_create_new: bool = False) -> Optional[Dict]:
        try:

            if self._is_all_fields_empty(card_info):
                print("⚠️ Insufficient OCR data (empty or missing customer name) - skipping database insert")
                return {
                    'customer_id': None,
                    'customer': None,
                    'history_id': None,
                    'checkin_id': None,
                    'action': 'skipped_insufficient_data'
                }

            customer_data = self._prepare_customer_data(card_info)
            print(f"🔍 Database save - customer_data: {customer_data}")

            existing_customer = None if force_create_new else self._find_existing_customer(customer_data)

            history_result = None
            action = 'created'

            if existing_customer:
                # <PERSON><PERSON><PERSON> tra xem có thay đổi thông tin không
                if self._has_customer_data_changed(existing_customer, customer_data):
                    # Có thay đổi: Tạo history record trước khi update
                    history_result = self.history_crud.create_from_customer(existing_customer.customer_id)
                    print(f"📝 Created history record (ID: {history_result.history_id}) before updating customer")

                    # Update customer với thông tin mới
                    customer_result = self.customer_crud.update_customer(
                        existing_customer.customer_id,
                        **customer_data
                    )
                    action = 'updated'
                    print(f"🔄 Updated customer (ID: {customer_result.customer_id}) with new information")
                else:
                    # Không có thay đổi: Không update, không tạo history
                    customer_result = existing_customer
                    action = 'no_change'
                    print(f"📋 Customer (ID: {customer_result.customer_id}) information unchanged, no update needed")
            else:
                # Khách hàng mới: Tạo mới
                customer_result = self.customer_crud.create_customer(**customer_data)
                action = 'created'
                print(f"🆕 Created new customer (ID: {customer_result.customer_id})")

            # Tạo hoặc cập nhật checkin record
            checkin_result = None
            if session_data:
                # Kiểm tra xem đã có checkin_id từ session chưa (đã tạo khi chụp ảnh)
                existing_checkin_id = session_data.get('existing_checkin_id')

                if existing_checkin_id:
                    # Cập nhật checkin record hiện có với customer_id và history_id
                    checkin_result = self.checkin_crud.update(
                        existing_checkin_id,
                        customer_id=customer_result.customer_id,
                        history_id=history_result.history_id if history_result else None,
                        card_img=session_data.get('card_image_path'),
                        face_img=session_data.get('face_image_path'),
                        ai_img=session_data.get('ai_image_path')
                    )
                    print(f"✅ Updated existing checkin record (ID: {existing_checkin_id}) with customer info")
                else:
                    # Tạo checkin record mới
                    checkin_result = self.checkin_crud.create_checkin(
                        customer_id=customer_result.customer_id,
                        history_id=history_result.history_id if history_result else None,
                        card_img=session_data.get('card_image_path'),
                        face_img=session_data.get('face_image_path'),
                        ai_img=session_data.get('ai_image_path')
                    )
                    print(f"✅ Created new checkin record (ID: {checkin_result.checkin_id})")

            result = {
                'customer_id': customer_result.customer_id,
                'customer': customer_result,
                'history_id': history_result.history_id if history_result else None,
                'checkin_id': checkin_result.checkin_id if checkin_result else None,
                'action': action
            }

            return result

        except Exception as e:
            print(f"❌ Database save error: {e}")
            import traceback
            traceback.print_exc()
            return None

    def _is_all_fields_empty(self, card_info: Dict) -> bool:
        required_fields = ['name', 'title', 'company', 'email', 'phone', 'website', 'address', 'other_info']
        
        non_empty_fields = []
        for field in required_fields:
            value = card_info.get(field, '').strip()
            if value:
                non_empty_fields.append(field)
        
        if not non_empty_fields:
            return True
        
        customer_name = card_info.get('name', '').strip()
        if not customer_name:
            print(f"⚠️ Customer name is empty - skipping database insert")
            return True
        
        return False  
    
    def _prepare_customer_data(self, card_info: Dict) -> Dict:
        return {
            'company_name': card_info.get('company', '').strip(),
            'customer_name': card_info.get('name', '').strip(),
            'customer_email': card_info.get('email', '').strip(),
            'customer_tel': card_info.get('phone', '').strip(),
            'customer_addr': card_info.get('address', '').strip(),
            'customer_title': card_info.get('title', '').strip(),
            'customer_web': card_info.get('website', '').strip(),
            'customer_infor': card_info.get('other_info', '').strip()  # Chỉ lưu other_info thay vì toàn bộ JSON
        }
    
    def _find_existing_customer(self, customer_data: Dict) -> Optional[object]:
        if customer_data.get('customer_email'):
            existing = self.customer_crud.get_by_email(customer_data['customer_email'])
            if existing:
                return existing

        if customer_data.get('customer_name'):
            existing_list = self.customer_crud.get_by_name(customer_data['customer_name'])
            exact_matches = [c for c in existing_list if c.customer_name == customer_data['customer_name']]

            if exact_matches:
                if len(exact_matches) > 1 and customer_data.get('customer_email'):
                    input_domain = customer_data['customer_email'].split('@')[-1] if '@' in customer_data['customer_email'] else ''
                    for customer in exact_matches:
                        if customer.customer_email and '@' in customer.customer_email:
                            customer_domain = customer.customer_email.split('@')[-1]
                            if customer_domain == input_domain:
                                return customer

                return exact_matches[0]

        return None

    def _has_customer_data_changed(self, existing_customer, new_customer_data: Dict) -> bool:
        """
        Kiểm tra xem thông tin customer có thay đổi không
        So sánh từng field để xác định có cần update không
        """
        try:
            # Danh sách các field cần so sánh
            fields_to_compare = [
                'company_name', 'customer_name', 'customer_email',
                'customer_tel', 'customer_addr', 'customer_title',
                'customer_web', 'customer_infor'
            ]

            for field in fields_to_compare:
                existing_value = getattr(existing_customer, field, '') or ''
                new_value = new_customer_data.get(field, '') or ''

                # Normalize strings for comparison
                existing_value = str(existing_value).strip()
                new_value = str(new_value).strip()

                if existing_value != new_value:
                    print(f"🔍 Field '{field}' changed: '{existing_value}' → '{new_value}'")
                    return True

            print("🔍 No changes detected in customer data")
            return False

        except Exception as e:
            print(f"⚠️ Error comparing customer data: {e}")
            # Nếu có lỗi, coi như có thay đổi để đảm bảo an toàn
            return True

    def get_customer_stats(self) -> Dict:
        try:
            return {
                'total_customers': self.customer_crud.count(),
                'total_checkins': self.checkin_crud.count(),
                'total_histories': self.history_crud.count(),
                'recent_checkins': len(self.checkin_crud.get_recent_checkins(10)),
                'today_checkins': len(self.checkin_crud.get_today_checkins())
            }
        except Exception:
            return {}

db_service = DatabaseService()
