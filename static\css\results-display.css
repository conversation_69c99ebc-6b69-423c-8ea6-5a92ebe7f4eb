/* ========================================
   RESULTS DISPLAY CSS - Card info and images display
   Styles for results sections, card information, and image galleries
   Includes Apple Design System card styles
   ======================================== */

/* ===== BUSINESS CARD STYLES (Simplified for Clarity) ===== */
.ocr-results-overlay {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90vw;
    max-width: 700px; /* Increased width as requested */
    /* Removed max-height and overflow-y to show all content */
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.9) 0%, rgba(118, 75, 162, 0.9) 100%); /* Matching app theme */
    border-radius: 16px;
    box-shadow: 0 15px 40px rgba(102, 126, 234, 0.25); /* Matching app shadow style */
    z-index: 9999; /* Ensure it's above everything */
    border: 1px solid rgba(255, 255, 255, 0.2); /* Matching app border style */
    backdrop-filter: blur(20px); /* Matching app blur effect */
    transition: opacity 0.3s ease, transform 0.3s ease;
    /* Force visibility when displayed */
    visibility: visible !important;
    opacity: 1 !important;
}

.ocr-results-grid {
    padding: 20px 25px 25px;
    display: flex;
    flex-direction: column;
    gap: 16px; /* Adjusted gap for better rhythm */
}

.ocr-result-item {
    display: flex;
    align-items: flex-start; /* Align items to the top for multi-line values */
    padding-bottom: 16px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.ocr-result-item:last-child {
    border-bottom: none;
    /* The padding-bottom from .ocr-result-item is now applied, creating consistent spacing */
}

.ocr-result-label {
    flex: 0 0 160px; /* Increased width for labels to prevent wrapping */
    font-size: 22px; /* Matched font size with value */
    font-weight: 500; /* Regular weight for distinction */
    color: rgba(255, 255, 255, 0.7); /* Softer label color */
}

.ocr-result-value {
    flex: 1;
    font-size: 23px; /* Larger font size */
    font-weight: 600; /* Semibold for emphasis */
    color: #ffffff;
    word-break: break-word;
    line-height: 1.7;
}

.ocr-result-value:empty::before {
    content: '—';
    color: rgba(255, 255, 255, 0.5);
}

.full-width {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px; /* Reduced gap for address field */
}

/* Business Card Header */
.ocr-results-header {
    background: rgba(255, 255, 255, 0.05);
    padding: 15px 25px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.15);
    text-align: center;
}

.ocr-results-title {
    font-size: 30px;
    font-weight: 600;
    color: white;
    margin: 0;
}

/* Ensure OCR results are always visible when displayed */
.ocr-results-overlay[style*="display: block"] {
    visibility: visible !important;
    opacity: 1 !important;
    z-index: 9999 !important;
}

/* Force display when JavaScript sets display: block */
.ocr-results-overlay[style*="display: block"] {
    display: block !important;
}









/* Responsive adjustments for 7-inch screens */
@media (max-width: 800px) {
    .ocr-results-overlay {
        width: 95vw;
        max-width: 350px;
        max-height: 80vh;
    }

    .ocr-results-grid {
        padding: 25px 20px;
        gap: 15px;
    }

    .ocr-result-item {
        padding: 10px 0;
    }

    .ocr-result-label {
        flex: 0 0 70px;
        font-size: 13px;
    }

    .ocr-result-value {
        font-size: 15px;
    }

    .full-width .ocr-result-label {
        font-size: 14px;
    }

    .full-width .ocr-result-value {
        font-size: 14px;
    }
}

@media (max-width: 600px) {
    .ocr-results-overlay {
        width: 98vw;
        max-width: 320px;
    }

    .ocr-results-grid {
        padding: 20px 15px;
        gap: 12px;
    }

    .ocr-result-item {
        padding: 8px 0;
    }

    .ocr-result-label {
        flex: 0 0 65px;
        font-size: 12px;
    }

    .ocr-result-value {
        font-size: 14px;
    }
}

/* ===== APPLE CARDS (Vibrant style) ===== */
.apple-card {
  background: var(--bg-card);
  border-radius: var(--radius-2xl);
  box-shadow: 0 10px 40px rgba(102, 126, 234, 0.2);
  overflow: hidden;
  transition: var(--transition);
  border: 2px solid rgba(255,255,255,0.3);
  backdrop-filter: blur(20px);
  margin: var(--spacing-lg);
}

.apple-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 60px rgba(102, 126, 234, 0.3);
  border-color: rgba(255,255,255,0.5);
}

.apple-card-header {
  padding: var(--spacing-xl);
  border-bottom: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-2xl) var(--radius-2xl) 0 0;
}

/* ========================================
   IMAGES DISPLAY STYLES
   ======================================== */

/* Images grid - Lưới hiển thị ảnh */
.images-grid {
    display: grid;                                        /* CSS Grid */
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); /* Cột tự động, tối thiểu 250px */
    gap: var(--spacing-lg);                               /* Khoảng cách lớn giữa các ảnh */
}

/* Image item - Item chứa ảnh */
.image-item {
    background: rgba(255, 255, 255, 0.05);               /* Background rất nhạt */
    border-radius: var(--radius-lg);                     /* Bo góc lớn */
    overflow: hidden;                                    /* Ẩn nội dung tràn */
    transition: all 0.3s ease;                          /* Smooth transition */
    border: 1px solid rgba(255, 255, 255, 0.1);         /* Subtle border */
}

.image-item:hover {
    transform: translateY(-4px);                         /* Lift effect */
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.2);         /* Enhanced shadow */
    border-color: rgba(102, 126, 234, 0.3);             /* Colored border on hover */
}

/* Image display - Container hiển thị ảnh */
.image-display {
    position: relative;                                  /* Để định vị overlay */
    aspect-ratio: 4/3;                                  /* Tỷ lệ 4:3 */
    overflow: hidden;                                    /* Ẩn nội dung tràn */
}

/* Image display img - Ảnh trong container */
.image-display img {
    width: 100%;                                         /* Chiều rộng 100% */
    height: 100%;                                        /* Chiều cao 100% */
    object-fit: cover;                                   /* Cắt để vừa khung */
    transition: transform 0.3s ease;                    /* Smooth zoom */
}

.image-item:hover .image-display img {
    transform: scale(1.05);                              /* Slight zoom on hover */
}

/* Image overlay - Overlay trên ảnh */
.image-overlay {
    position: absolute;                                  /* Định vị tuyệt đối */
    bottom: 0;                                          /* Ở dưới cùng */
    left: 0;                                            /* Từ trái */
    right: 0;                                           /* Đến phải */
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8)); /* Gradient từ trong suốt đến đen */
    color: white;                                        /* Màu chữ trắng */
    padding: var(--spacing-md);                          /* Padding vừa */
    font-weight: var(--font-weight-semibold);           /* Độ đậm cao */
    transform: translateY(100%);                         /* Hidden by default */
    transition: transform 0.3s ease;                    /* Smooth reveal */
}

.image-item:hover .image-overlay {
    transform: translateY(0);                            /* Show on hover */
}

/* Image actions - Khu vực hành động (nút download) */
.image-actions {
    padding: var(--spacing-md);                          /* Padding vừa */
    text-align: center;                                  /* Căn giữa */
    background: rgba(255, 255, 255, 0.02);               /* Subtle background */
}

/* Download button - Nút tải về */
.download-btn {
    display: inline-block;                               /* Hiển thị inline-block */
    background: var(--success-gradient);                 /* Background gradient xanh */
    color: white;                                        /* Màu chữ trắng */
    padding: var(--spacing-sm) var(--spacing-md);       /* Padding nhỏ dọc, vừa ngang */
    border-radius: var(--radius-md);                     /* Bo góc vừa */
    text-decoration: none;                               /* Không gạch chân */
    font-weight: var(--font-weight-semibold);           /* Độ đậm cao */
    transition: all 0.3s ease;                          /* Hiệu ứng chuyển đổi */
    font-size: 14px;                                    /* Font size */
    border: none;                                        /* No border */
    cursor: pointer;                                     /* Pointer cursor */
}

/* Download button hover - Hiệu ứng hover nút tải về */
.download-btn:hover {
    transform: translateY(-2px);                         /* Di chuyển lên 2px */
    box-shadow: 0 8px 25px rgba(67, 233, 123, 0.4);     /* Đổ bóng xanh */
    background: linear-gradient(135deg, #4ade80, #22c55e); /* Brighter gradient */
}

/* ========================================
   GENERATED IMAGES SPECIFIC STYLES
   ======================================== */

/* Generated image item */
.generated-image-item {
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-lg);
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid rgba(102, 126, 234, 0.2);
}

.generated-image-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 30px rgba(102, 126, 234, 0.2);
    border-color: rgba(102, 126, 234, 0.4);
}

/* Generated image */
.generated-image {
    width: 100%;
    height: auto; /* Để ảnh giữ tỷ lệ khung hình */
    object-fit: contain; /* Thay đổi từ cover sang contain */
    display: block;
    transition: transform 0.3s ease;
    max-height: 80vh; /* Giới hạn chiều cao tối đa */
}

.generated-image-item:hover .generated-image {
    transform: scale(1.02);
}

/* ========================================
   DATA STATES
   ======================================== */

/* Loading state for card info */
.card-info-loading {
    color: var(--color-text-secondary);
    font-style: italic;
    opacity: 0.7;
}

/* Error state */
.card-info-error {
    color: var(--color-error);
    font-style: italic;
}

/* Success state */
.card-info-success {
    color: var(--color-text);
    font-style: normal;
}

/* No data state */
.card-info-no-data {
    color: var(--color-text-secondary);
    opacity: 0.6;
}

/* ========================================
   RESULT STATISTICS
   ======================================== */

/* Stats container */
.result-stats {
    display: flex;
    justify-content: space-around;
    align-items: center;
    padding: var(--spacing-md);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-md);
    margin-bottom: var(--spacing-lg);
}

/* Stat item */
.stat-item {
    text-align: center;
}

.stat-value {
    font-size: 24px;
    font-weight: var(--font-weight-bold);
    color: var(--color-primary);
    display: block;
}

.stat-label {
    font-size: 12px;
    color: var(--color-text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-top: var(--spacing-xs);
}

/* ========================================
   QUALITY INDICATORS
   ======================================== */

/* Quality badge */
.quality-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: var(--radius-sm);
    font-size: 12px;
    font-weight: var(--font-weight-semibold);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.quality-high {
    background: var(--color-success);
    color: white;
}

.quality-medium {
    background: var(--color-warning);
    color: white;
}

.quality-low {
    background: var(--color-error);
    color: white;
}
