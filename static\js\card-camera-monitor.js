class CardCameraMonitor {
    constructor() {
        this.isCardCameraFrozen = false;
        this.fastPollInterval = null;
        this.slowPollInterval = null;

        this.config = {
            fastPollInterval: 1000,
            slowPollInterval: 10000,
            fastPollTimeout: 60000,
            apiEndpoint: '/api/session_status'
        };

        this.elements = {
            cardImg: null,
            statusDot: null,
            statusText: null
        };
    }

    init() {
        this.cacheElements();
        this.startSlowPolling();
        this.startFastPolling();
    }

    cacheElements() {
        this.elements.cardImg = document.getElementById('cam0');
        this.elements.statusDot = document.getElementById('status0');
        this.elements.statusText = document.getElementById('cardStatus');
    }

    async checkAndFreezeCardCamera() {
        try {
            const response = await fetch(this.config.apiEndpoint);
            const data = await response.json();

            if (data.status === 'success' && data.session) {
                const session = data.session;

                if (session.card_captured && !this.isCardCameraFrozen) {
                    const cardImagePath = session.card_image;
                    if (cardImagePath) {
                        this.freezeCardCamera(cardImagePath);
                    } else {
                        this.freezeCardCameraGeneric();
                    }

                    if (typeof startAutoProcessingMonitor === 'function') {
                        startAutoProcessingMonitor();
                    }
                }
            }
        } catch (error) {
            // Silent error handling
        }
    }

    freezeCardCamera(imagePath) {
        if (this.elements.cardImg) {
            this.elements.cardImg.src = '/' + imagePath + '?t=' + new Date().getTime();
            this.elements.cardImg.style.border = '3px solid #43e97b';
            this.isCardCameraFrozen = true;
        }

        this.updateVisualFeedback();
        this.stopFastPolling();
    }

    freezeCardCameraGeneric() {
        if (this.elements.cardImg) {
            this.elements.cardImg.style.border = '3px solid #43e97b';
            this.isCardCameraFrozen = true;
        }

        this.updateVisualFeedback();
        this.stopFastPolling();
    }

    updateVisualFeedback() {
        if (this.elements.statusDot) {
            this.elements.statusDot.style.background = '#43e97b';
            this.elements.statusDot.className = 'status-dot status-captured';
        }

        if (this.elements.statusText) {
            this.elements.statusText.textContent = '✅';
            this.elements.statusText.style.color = '#43e97b';
        }
    }

    startFastPolling() {
        if (this.fastPollInterval) {
            clearInterval(this.fastPollInterval);
        }

        this.fastPollInterval = setInterval(() => {
            if (!this.isCardCameraFrozen) {
                this.checkAndFreezeCardCamera();
            } else {
                this.stopFastPolling();
            }
        }, this.config.fastPollInterval);

        setTimeout(() => {
            this.stopFastPolling();
        }, this.config.fastPollTimeout);
    }

    stopFastPolling() {
        if (this.fastPollInterval) {
            clearInterval(this.fastPollInterval);
            this.fastPollInterval = null;
        }
    }

    startSlowPolling() {
        if (this.slowPollInterval) {
            clearInterval(this.slowPollInterval);
        }

        this.slowPollInterval = setInterval(() => {
            if (!this.isCardCameraFrozen) {
                this.checkAndFreezeCardCamera();
            }
        }, this.config.slowPollInterval);
    }

    stopSlowPolling() {
        if (this.slowPollInterval) {
            clearInterval(this.slowPollInterval);
            this.slowPollInterval = null;
        }
    }

    async reset() {
        try {
            const response = await fetch('/reset_card_camera', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            
            const data = await response.json();
            
            if (data.status === 'success') {
                console.log('✅ Card camera reset successfully');
                
                this.isCardCameraFrozen = false;
                
                if (this.elements.cardImg) {
                    const timestamp = new Date().getTime();
                    const url = new URL('/card_detection_feed', window.location.origin);
                    url.searchParams.set('t', timestamp);
                    this.elements.cardImg.src = url.toString();
                    this.elements.cardImg.style.border = '';
                }
                
                if (this.elements.statusDot) {
                    this.elements.statusDot.style.background = '';
                    this.elements.statusDot.className = 'status-dot status-ready';
                }
                
                if (this.elements.statusText) {
                    this.elements.statusText.textContent = '📄';
                    this.elements.statusText.style.color = '';
                }
                
                this.stopFastPolling();
                this.startFastPolling();
                
            } else {
                console.error('❌ Failed to reset card camera:', data.message);
            }
        } catch (error) {
            console.error('❌ Error resetting card camera:', error);
        }
    }

    destroy() {
        this.stopFastPolling();
        this.stopSlowPolling();
    }
}

window.cardCameraMonitor = null;

document.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => {
        if (!window.cardCameraMonitor) {
            window.cardCameraMonitor = new CardCameraMonitor();
            window.cardCameraMonitor.init();
        }
    }, 1000);
});

if (typeof module !== 'undefined' && module.exports) {
    module.exports = CardCameraMonitor;
}
