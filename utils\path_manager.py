#!/usr/bin/env python3
"""
PathManager - Centralized path handling utility for AI_Gen
Quản lý tập trung tất cả logic xử lý đường dẫn cho AI_Gen

Chức năng chính:
- Tạo timestamp thống nhất
- Sanitize filename 
- T<PERSON><PERSON> đường dẫn cho captured images
- Tạo đường dẫn cho AI generated images
- Quản lý directory structure
"""

import os
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, Optional, Union


class PathManager:
    """Centralized path management for AI_Gen application"""
    
    # Directory constants
    UPLOAD_DIR = "static/img"
    OUTPUT_DIR = "outputs" 
    PROMPTS_DIR = "prompts"
    
    # File naming patterns
    CARD_PATTERN = "business_card_{timestamp}.jpg"
    FACE_PATTERN = "face_{timestamp}.jpg"
    AI_IMAGE_PATTERN = "{person_name}_AI_{timestamp}_v{variant}.png"
    OCR_JSON_PATTERN = "{person_name}_card_info_{timestamp}.json"
    OCR_TXT_PATTERN = "{person_name}_card_info_{timestamp}.txt"
    
    def __init__(self):
        """Initialize PathManager and ensure required directories exist"""
        self._ensure_base_directories()
        self._session_timestamp = None  # Shared timestamp for session
        
    def _ensure_base_directories(self):
        """Ensure all base directories exist"""
        directories = [self.UPLOAD_DIR, self.OUTPUT_DIR, self.PROMPTS_DIR]
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
    
    def get_session_timestamp(self) -> str:
        """Get or create session timestamp (shared across operations)"""
        if self._session_timestamp is None:
            self._session_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return self._session_timestamp
    
    def reset_session_timestamp(self):
        """Reset session timestamp for new session"""
        self._session_timestamp = None
    
    def get_unix_timestamp(self) -> int:
        """Get current unix timestamp for camera operations"""
        return int(time.time())
    
    def sanitize_person_name(self, name: str) -> str:
        """Sanitize person name for use in filenames"""
        if not name or name.strip() == '' or name.lower() == 'n/a':
            return 'Unknown_Person'
        
        # Clean and sanitize
        sanitized = name.strip()
        sanitized = sanitized.replace(' ', '_')
        sanitized = ''.join(c for c in sanitized if c.isalnum() or c in ['_', '-'])
        
        # Limit length
        if len(sanitized) > 50:
            sanitized = sanitized[:50]
        
        return sanitized or 'Unknown_Person'
    
    def get_captured_image_path(self, image_type: str, timestamp: Optional[int] = None) -> str:
        """
        Get path for captured image (card or face)
        
        Args:
            image_type: 'card' or 'face'
            timestamp: Optional unix timestamp, uses current if None
            
        Returns:
            Full path for captured image
        """
        if timestamp is None:
            timestamp = self.get_unix_timestamp()
            
        if image_type == 'card':
            filename = f"business_card_{timestamp}.jpg"
        elif image_type == 'face':
            filename = f"face_{timestamp}.jpg"
        else:
            filename = f"{image_type}_{timestamp}.jpg"
            
        return os.path.join(self.UPLOAD_DIR, filename)
    
    def get_person_directory(self, person_name: str) -> Path:
        """Get person-specific directory path"""
        sanitized_name = self.sanitize_person_name(person_name)
        person_dir = Path(self.OUTPUT_DIR) / sanitized_name
        person_dir.mkdir(parents=True, exist_ok=True)
        return person_dir
    
    def get_ai_image_path(self, person_name: str, variant: int = 1, 
                         timestamp: Optional[str] = None, 
                         prefix: str = "AI") -> Path:
        """
        Get path for AI generated image
        
        Args:
            person_name: Name from business card
            variant: Image variant number (1, 2, etc.)
            timestamp: Optional timestamp, uses session timestamp if None
            prefix: Prefix for filename (AI, enhanced, dollhouse, etc.)
            
        Returns:
            Full path for AI image
        """
        if timestamp is None:
            timestamp = self.get_session_timestamp()
            
        sanitized_name = self.sanitize_person_name(person_name)
        person_dir = self.get_person_directory(person_name)
        
        filename = f"{sanitized_name}_{prefix}_{timestamp}_v{variant}.png"
        return person_dir / filename
    
    def get_ocr_file_paths(self, person_name: str, 
                          timestamp: Optional[str] = None) -> Dict[str, Path]:
        """
        Get paths for OCR output files (JSON and TXT)
        
        Args:
            person_name: Name from business card
            timestamp: Optional timestamp, uses session timestamp if None
            
        Returns:
            Dictionary with 'json' and 'txt' paths
        """
        if timestamp is None:
            timestamp = self.get_session_timestamp()
            
        sanitized_name = self.sanitize_person_name(person_name)
        person_dir = self.get_person_directory(person_name)
        
        return {
            'json': person_dir / f"{sanitized_name}_card_info_{timestamp}.json",
            'txt': person_dir / f"{sanitized_name}_card_info_{timestamp}.txt"
        }
    
    def get_relative_path_for_web(self, absolute_path: Union[str, Path, None]) -> str:
        """
        Convert absolute path to relative path for web serving
        
        Args:
            absolute_path: Absolute file path (can be None)
            
        Returns:
            Relative path suitable for web URLs
        """
        if not absolute_path:
            return ""
            
        path = Path(absolute_path)
        
        # If path contains 'static', return from static onwards
        if 'static' in path.parts:
            static_index = path.parts.index('static')
            return str(Path(*path.parts[static_index:]))
        
        # If path contains 'outputs', return from outputs onwards  
        if 'outputs' in path.parts:
            outputs_index = path.parts.index('outputs')
            return str(Path(*path.parts[outputs_index:]))
            
        return str(path)


# Global PathManager instance
path_manager = PathManager()


# Convenience functions for backward compatibility
def get_session_timestamp() -> str:
    """Get session timestamp"""
    return path_manager.get_session_timestamp()

def reset_session_timestamp():
    """Reset session timestamp"""
    path_manager.reset_session_timestamp()

def sanitize_person_name(name: str) -> str:
    """Sanitize person name"""
    return path_manager.sanitize_person_name(name)

def get_captured_image_path(image_type: str, timestamp: Optional[int] = None) -> str:
    """Get captured image path"""
    return path_manager.get_captured_image_path(image_type, timestamp)

def get_ai_image_path(person_name: str, variant: int = 1, 
                     timestamp: Optional[str] = None, prefix: str = "AI") -> Path:
    """Get AI image path"""
    return path_manager.get_ai_image_path(person_name, variant, timestamp, prefix)

def get_person_directory(person_name: str) -> Path:
    """Get person directory"""
    return path_manager.get_person_directory(person_name)
