/* ========================================
   LAYOUT CORE CSS - Main layout and navigation
   Core layout styles for single page application
   Includes design tokens, base styles, and navigation
   ======================================== */

/* ===== VIBRANT DESIGN TOKENS ===== */
:root {
  /* Vibrant Color Palette - Bắt mắt và tươi sáng */
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);

  /* Background Gradients */
  --bg-main: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
  --bg-hero: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  --bg-card: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.9) 100%);
  --bg-surface: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);

  /* Vibrant Colors */
  --color-primary: #667eea;
  --color-secondary: #f093fb;
  --color-accent: #4facfe;
  --color-success: #43e97b;
  --color-warning: #fee140;
  --color-danger: #fa709a;
  --color-error: #fa709a;

  /* Text Colors */
  --color-text: #2d3748;
  --color-text-primary: #2d3748;
  --color-text-secondary: #4a5568;
  --color-text-light: #718096;
  --color-text-white: #10053a;
  --color-background: #ffffff;
  --color-surface: #f7fafc;

  /* Typography - Vietnamese-supported fonts */
  --font-family: 'Nunito', 'Open Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  --font-weight-light: 300;
  --font-weight-regular: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* Enhanced spacing system */
  --spacing-xs: 6px;
  --spacing-sm: 12px;
  --spacing-md: 20px;
  --spacing-lg: 32px;
  --spacing-xl: 48px;
  --spacing-2xl: 64px;
  --spacing-3xl: 80px;
  --spacing-4xl: 96px;

  /* Border radius - Enhanced rounded corners */
  --radius-sm: 12px;
  --radius-md: 18px;
  --radius-lg: 24px;
  --radius-xl: 32px;
  --radius-2xl: 40px;
  --radius-3xl: 48px;

  /* Shadows - Apple's subtle depth */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.2);

  /* Transitions - Apple's fluid animations */
  --transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* ===== RESET & BASE ===== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  width: 100vw !important;                    
  overflow-x: hidden !important;            
  margin: 0 !important;                       
  padding: 0 !important;                     
}

body {
  font-family: var(--font-family);
  font-weight: var(--font-weight-regular);
  color: var(--color-text);
  background: var(--bg-main);
  background-attachment: fixed;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  min-height: 100vh;
  width: 100vw !important;                     /* VIEWPORT WIDTH - absolute full screen */
  overflow-x: hidden !important;               /* Prevent horizontal scroll */
  margin: 0 !important;                        /* NO margins */
  padding: 0 !important;                       /* NO padding */
}

/* ===== APPLE-STYLE LAYOUT ===== */
.apple-container {
  width: 100% !important;                      /* FULL WIDTH - removed max-width limit */
  margin: 0 !important;                        /* No auto centering for full width */
  padding: 0 !important;                       /* NO PADDING for true full width */
}

/* GLOBAL SCROLL & ANIMATION */
html { scroll-behavior: smooth; }
body.no-scroll { overflow: hidden; height: 100vh; }
body.allow-scroll { overflow-y: auto; height: auto; }

/* MAIN LAYOUT - ABSOLUTE FULL WIDTH CONTAINER */
.single-page-container {
    width: 100vw !important;                     /* VIEWPORT WIDTH - absolute full screen */
    margin: 60px 0 0 0 !important;               /* No margins at all */
    padding: 0 !important;                       /* NO PADDING whatsoever */
    min-height: calc(100vh - 60px) !important;   /* More space */
    box-sizing: border-box !important;           /* Include borders in width calculation */
}

/* PAGE HEADER - MAXIMUM COMPACT for camera space */
.page-header {
    text-align: center;
    margin-bottom: 4px !important;              /* ULTRA MINIMAL spacing */
    padding: 8px 0 !important;                  /* ULTRA REDUCED padding */
    background: var(--bg-hero);
    border-radius: 8px !important;              /* MINIMAL radius */
}

.page-title {
    font-size: clamp(20px, 2.5vw, 28px) !important; /* ULTRA COMPACT font */
    font-weight: var(--font-weight-bold);
    color: var(--color-text-white);
    margin-bottom: 2px !important;              /* ULTRA MINIMAL spacing */
    text-shadow: 0 4px 20px rgba(0,0,0,0.3);
}

.page-subtitle {
    font-size: 18px;
    color: rgba(255,255,255,0.8);
    margin: 0;
}

/* WORKFLOW SECTIONS */

/* Workflow sections - ULTRA COMPACT for maximum camera space */
.workflow-section {
    margin-bottom: var(--spacing-xs);            /* MINIMAL spacing */
    background: var(--bg-card);                  /* Background card */
    border-radius: var(--radius-md);             /* SMALLER border radius */
    box-shadow: 0 15px 40px rgba(102, 126, 234, 0.25); /* Giảm đổ bóng */
    overflow: hidden;                            /* Ẩn nội dung tràn */
    border: 2px solid rgba(255,255,255,0.3);     /* Giảm viền */
    backdrop-filter: blur(20px);                 /* Giảm hiệu ứng blur */
    scroll-margin-top: 60px;                     /* REDUCED scroll margin */
}

/* Section header - Header của từng section */
.section-header {
    background: var(--primary-gradient);         /* Background gradient */
    color: white;                                /* Màu chữ trắng */
    padding: var(--spacing-lg);                  /* Padding */
    text-align: center;                          /* Căn giữa */
    position: relative;                          /* Để định vị status */
}

/* Section title - Tiêu đề section */
.section-title {
    font-size: 24px;                            /* Kích thước font */
    font-weight: var(--font-weight-bold);       /* Độ đậm */
    margin: 0;                                   /* Không margin */
    display: flex;                               /* Flexbox */
    align-items: center;                         /* Căn giữa theo chiều dọc */
    justify-content: center;                     /* Căn giữa theo chiều ngang */
    gap: var(--spacing-sm);                      /* Khoảng cách giữa icon và text */
}

/* Section subtitle - Phụ đề section */
.section-subtitle {
    color: rgba(255, 255, 255, 0.8);            /* Màu chữ trắng trong suốt */
    margin: var(--spacing-xs) 0 0 0;            /* Margin trên */
    font-size: 16px;                            /* Kích thước font */
}

/* STATUS INDICATORS */

/* Section status - Trạng thái section */
.section-status {
    position: absolute;                          /* Định vị tuyệt đối */
    top: var(--spacing-md);                      /* Vị trí từ trên */
    right: var(--spacing-lg);                    /* Vị trí từ phải */
    display: flex;                               /* Flexbox */
    align-items: center;                         /* Căn giữa theo chiều dọc */
    gap: var(--spacing-xs);                      /* Khoảng cách giữa các element */
}

/* Status indicator dot - Chấm trạng thái */
.status-indicator {
    width: 12px;                                 /* Chiều rộng */
    height: 12px;                                /* Chiều cao */
    border-radius: 50%;                          /* Hình tròn */
    background: var(--color-warning);            /* Màu mặc định */
}

/* Status colors - Màu sắc trạng thái */
.status-pending { background: var(--color-warning); }     /* Chờ xử lý */
.status-processing {                                      /* Đang xử lý */
    background: var(--color-primary);
    animation: pulse 2s infinite;                         /* Hiệu ứng nhấp nháy */
}
.status-complete { background: var(--color-success); }    /* Hoàn thành */
.status-error { background: var(--color-error); }        /* Lỗi */

/* Pulse animation - Hiệu ứng nhấp nháy */
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Section body - Nội dung section (ultra compact) */
.section-body {
    padding: var(--spacing-sm);                  /* Giảm padding tối đa để gọn nhất */
}

/* ========================================
   APPLE NAVIGATION SYSTEM
   ======================================== */

/* ===== NAVIGATION (Vibrant style) ===== */
.apple-nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.95) 0%, rgba(118, 75, 162, 0.95) 100%);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  z-index: 1000;
  transition: var(--transition);
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
}

.apple-nav-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  position: relative;
}

.apple-logo {
  font-size: 20px;
  font-weight: var(--font-weight-bold);
  color: var(--color-text-white);
  text-decoration: none;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.apple-nav-links {
  display: flex;
  gap: var(--spacing-xl);
  list-style: none;
}

.apple-nav-link {
  font-size: 16px;
  color: var(--color-text-white);
  text-decoration: none;
  transition: var(--transition);
  font-weight: var(--font-weight-medium);
  text-shadow: 0 1px 2px rgba(0,0,0,0.2);
}

.apple-nav-link:hover {
  color: #fff;
  text-shadow: 0 0 10px rgba(255,255,255,0.8);
  transform: translateY(-1px);
}

/* Navigation logo image */
.nav-logo-img {
  height: 50px;
  width: auto;
  object-fit: contain;
}

/* Admin Badge - Thông báo admin */
.admin-badge {
  position: absolute;
  top: 50%;
  right: var(--spacing-lg);
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  color: white;
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--radius-full);
  font-size: 14px;
  font-weight: var(--font-weight-bold);
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.3);
  z-index: 1001;
}

.admin-badge-icon {
  font-size: 16px;
  animation: rotate 3s linear infinite;
}

.admin-badge-text {
  font-size: 12px;
  letter-spacing: 1px;
  text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

/* Admin badge animations */
@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Responsive admin badge */
@media (max-width: 768px) {
  .admin-badge {
    right: var(--spacing-md);
    padding: var(--spacing-xs);
  }
  
  .admin-badge-text {
    display: none;
  }
}

/* ========================================
   ADMIN MODE LAYOUT
   ======================================== */

/* Admin mode body adjustments */
body.admin-mode .single-page-container {
  padding-top: 80px; /* Đẩy content lên trên hơn */
  height: calc(100vh - 80px); /* Set height để tránh overflow */
  display: flex;
  flex-direction: column;
  justify-content: right;
}

body.admin-mode .workflow-section {
  margin-top: 0; /* Loại bỏ margin top để camera grid sát lên trên */
  flex: 0 0 auto; /* Không grow, chỉ take space cần thiết */
}

/* Hiển thị admin controls trong admin mode */
body.admin-mode #adminControlsSection {
  display: flex !important; /* Force hiển thị với flex */
  flex: 1; /* Take remaining space */
  margin: var(--spacing-md) 0 0 0; /* Margin top only */
}

/* Thu nhỏ camera grid trong admin mode */
body.admin-mode .section-body {
  height: 350px; /* Thu nhỏ height để còn chỗ cho admin controls */
  display: flex; /* Flexbox để camera-grid fill full */
  flex-direction: column; /* Column layout */
}

body.admin-mode .camera-grid {
  height: 80%; /* 70% height camera grid */
  display: flex; /* Flexbox để camera containers fill full */
  gap: var(--spacing-md); /* Khoảng cách giữa cameras */
}

body.admin-mode .camera-container {
  height: 100%; /* Full height container */
  flex: 1; /* Flex để container chiếm hết không gian */
}

body.admin-mode .camera-frame {
  height: 100%; /* Full height khung camera */
  display: flex; /* Flexbox để camera-stream fill full */
}

body.admin-mode .camera-stream {
  height: 100%; /* Full height với camera-container */
  width: 100%; /* Full width với camera-container */
  object-fit: cover;
}

/* Admin Controls Section - Vibrant Glass Design */
body.admin-mode .section-body .admin-controls-section {
  display: flex; /* Hiển thị trong admin mode */
  flex-direction: column;
  height: 20%; /* Chiếm phần trống còn lại */
  background: linear-gradient(135deg, 
    rgba(102, 126, 234, 0.95) 0%, 
    rgba(118, 75, 162, 0.9) 50%, 
    rgba(240, 147, 251, 0.85) 100%);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-xl);
  margin-top: var(--spacing-sm);
  padding: var(--spacing-lg);
  box-shadow: 
    0 8px 32px rgba(102, 126, 234, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(16px);
  position: relative;
  overflow: hidden;
}

/* Glass effect overlay */
body.admin-mode .section-body .admin-controls-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.1) 0%, 
    rgba(255, 255, 255, 0.05) 50%, 
    rgba(255, 255, 255, 0.02) 100%);
  border-radius: inherit;
  pointer-events: none;
}

.admin-controls-header {
  text-align: center;
  margin-bottom: 0;
  position: relative;
  z-index: 1;
}

.admin-controls-title {
  font-size: 28px;
  font-weight: var(--font-weight-bold);
  color: white;
  margin: 0;
  letter-spacing: normal;
  text-transform: none;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.admin-controls-buttons {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--spacing-md);
  position: relative;
  z-index: 1;
  flex-wrap: wrap;
  margin-top: -var(--spacing-xs);
}

.admin-btn {
  padding: var(--spacing-md) var(--spacing-lg);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: var(--radius-lg);
  font-size: 20px !important;
  font-weight: var(--font-weight-bold);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  min-width: 120px;
  justify-content: center;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(8px);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  flex: 1;
  max-width: 200px;
  min-height: 60px;
  color: white;
  letter-spacing: normal;
  text-transform: none;
}

.admin-btn.capture-card-btn {
  background: linear-gradient(135deg, 
    rgba(59, 130, 246, 0.3) 0%, 
    rgba(59, 130, 246, 0.2) 100%);
  color: white;
  border-color: rgba(59, 130, 246, 0.4);
  box-shadow: 
    0 4px 16px rgba(59, 130, 246, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.admin-btn.capture-card-btn:hover {
  background: linear-gradient(135deg, 
    rgba(59, 130, 246, 0.4) 0%, 
    rgba(59, 130, 246, 0.3) 100%);
  transform: translateY(-2px);
  box-shadow: 
    0 8px 24px rgba(59, 130, 246, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

.admin-btn.capture-face-btn {
  background: linear-gradient(135deg, 
    rgba(16, 185, 129, 0.3) 0%, 
    rgba(16, 185, 129, 0.2) 100%);
  color: white;
  border-color: rgba(16, 185, 129, 0.4);
  box-shadow: 
    0 4px 16px rgba(16, 185, 129, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.admin-btn.capture-face-btn:hover {
  background: linear-gradient(135deg, 
    rgba(16, 185, 129, 0.4) 0%, 
    rgba(16, 185, 129, 0.3) 100%);
  transform: translateY(-2px);
  box-shadow: 
    0 8px 24px rgba(16, 185, 129, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
}


.admin-btn.reset-btn {
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.15) 0%, 
    rgba(255, 255, 255, 0.05) 100%);
  color: rgba(255, 255, 255, 0.9);
  border-color: rgba(255, 255, 255, 0.25);
  box-shadow: 
    0 2px 12px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.admin-btn.reset-btn:hover {
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.25) 0%, 
    rgba(255, 255, 255, 0.15) 100%);
  transform: translateY(-2px);
  box-shadow: 
    0 6px 20px rgba(0, 0, 0, 0.12),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.4);
}

.admin-btn.reset-btn:active {
  transform: translateY(0);
  box-shadow: 
    0 2px 8px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

/* Admin Controls Section Default - Ẩn mặc định khi không ở admin mode */
.admin-controls-section {
  display: none; /* Ẩn mặc định */
  flex-direction: column;
  justify-content: center;
}

.admin-controls-container {
  max-width: 1200px;
  margin: 0 auto;
}

/* Duplicate admin-controls-header và admin-controls-title đã được xóa 
   - sử dụng definition ở trên (dòng 437-452) */

.admin-controls-subtitle {
  font-size: 14px;
  color: var(--color-text-secondary);
  margin: 0;
}

/* Admin Controls Grid */
.admin-controls-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing-md);
  max-width: 600px;
  margin: 0 auto;
}



.admin-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.admin-btn-icon {
  font-size: 20px;
  margin-bottom: var(--spacing-xs);
}

.admin-btn-text {
  font-size: 11px;
  font-weight: var(--font-weight-bold);
  letter-spacing: 0.3px;
}

/* Removed unused button color classes - using capture-card-btn, capture-face-btn, generate-btn, reset-btn instead */

/* Admin button states */
.admin-btn:active {
  transform: translateY(-2px);
}

.admin-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.admin-btn:disabled:hover {
  transform: none;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

/* Responsive admin controls */
@media (max-width: 768px) {
  .admin-controls-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-sm);
  }
  
  .admin-btn {
    min-height: 70px;
    padding: var(--spacing-sm);
  }
  
  .admin-btn-icon {
    font-size: 18px;
  }
  
  .admin-btn-text {
    font-size: 10px;
  }
}

/* Navigation title */
.nav-title {
  color: white;
  font-size: 35px !important;                     /* LARGER FONT - tăng từ 24px lên 35px */
  font-weight: var(--font-weight-bold);
  margin: 0;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

/* ========================================
   HERO SECTION
   ======================================== */

/* ===== HERO SECTION (Vibrant style) ===== */
.apple-hero {
  padding: 140px 0 100px;
  text-align: center;
  background: var(--bg-hero);
  position: relative;
  overflow: hidden;
}

.apple-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 20%, rgba(255,255,255,0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(255,255,255,0.1) 0%, transparent 50%);
  pointer-events: none;
}

.apple-hero-title {
  font-size: clamp(40px, 6vw, 72px);
  font-weight: var(--font-weight-bold);
  line-height: 1.1;
  margin-bottom: var(--spacing-lg);
  color: var(--color-text-white);
  text-shadow: 0 4px 20px rgba(0,0,0,0.3);
  position: relative;
  z-index: 1;
}

.apple-hero-subtitle {
  font-size: 24px;
  font-weight: var(--font-weight-medium);
  color: rgba(255,255,255,0.9);
  margin-bottom: var(--spacing-2xl);
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
  text-shadow: 0 2px 10px rgba(0,0,0,0.2);
  position: relative;
  z-index: 1;
}

/* ===== NAVIGATION ACTIONS ===== */
.nav-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.nav-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: rgba(255, 255, 255, 0.2);
  color: var(--color-text-white);
  text-decoration: none;
  border-radius: 8px;
  font-weight: var(--font-weight-medium);
  font-size: 14px;
  transition: var(--transition);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  text-shadow: 0 1px 2px rgba(0,0,0,0.2);
}

.nav-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  color: #fff;
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  text-shadow: 0 0 10px rgba(255,255,255,0.8);
}

.nav-btn i {
  font-size: 16px;
}

.search-btn {
  background: rgba(0, 122, 255, 0.8);
  border-color: rgba(0, 122, 255, 0.5);
}

.search-btn:hover {
  background: rgba(0, 122, 255, 1);
  border-color: rgba(0, 122, 255, 0.8);
}

/* Responsive navigation */
@media (max-width: 768px) {
  .nav-btn span {
    display: none;
  }

  .nav-btn {
    padding: 10px 12px;
  }

  .apple-nav-content {
    padding: 0 var(--spacing-md);
  }
}
