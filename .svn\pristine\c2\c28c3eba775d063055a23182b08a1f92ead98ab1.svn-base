# card_1_direct.py
import cv2
import numpy as np
import os
import sys
import time
# REMOVED: from ultralytics import YOLO - moved to lazy loading
import torch

sys.path.append(os.path.dirname(os.path.dirname(__file__)))
from utils.path_manager import path_manager

FILE_DIR = os.path.dirname(os.path.abspath(__file__))
MODEL_PATH = os.path.join(FILE_DIR, "bamboo_all.pt")
device = "cuda:0" if torch.cuda.is_available() else "cpu"

# Load YOLO model lazily to avoid '_C' error
yolo_model = None

def get_yolo_model():
    global yolo_model
    if yolo_model is None:
        try:
            # Import YOLO locally to avoid '_C' error
            from ultralytics import YOLO
            yolo_model = YOLO(MODEL_PATH)
            # Don't use .to(device) to avoid CUDA issues
            print(f"✅ YOLO model loaded: {MODEL_PATH}")
        except Exception as e:
            print(f"❌ Error loading YOLO model: {e}")
            return None
    return yolo_model

# REMOVED: Circular import - camera_controller will be passed as parameter
# try:
#     from controllers.camera_controller import camera_controller
# except ImportError:
#     camera_controller = None

def detect_card_direct(frame_source = None, roi_rect=(100, 120, 500, 300), model = None, cam_index = 0, camera_controller=None):
    """
    Stream camera trực tiếp + YOLO detect card.
    Nhấn ESC để thoát.
    """
    # Load YOLO model if not provided
    if model is None:
        model = get_yolo_model()
        if model is None:
            print("❌ Cannot load YOLO model")
            return
    
    stable_frames = 150
    rearm_absence_frames = 50
    jpeg_quality = 85
    return_crop = True
    if frame_source is None:
        cap = cv2.VideoCapture(cam_index)
        if not cap.isOpened():
            yield bgr_to_mjpeg(np.zeros((240,320,3), np.uint8))
            print("[ERROR] Cannot open camera")
            return
        use_external_frames = False
    else:
        cap = None
        use_external_frames = True   

    stable = 0
    armed = True
    absence_cnt = 0
    saved = False
    captured_image = None
    frame_count = 0

    def clamp_roi(x, y, w, h, W, H):
        x = max(0, min(x, W - 1))
        y = max(0, min(y, H - 1))
        w = max(1, min(w, W - x))
        h = max(1, min(h, H - y))
        return x, y, w, h

    def bgr_to_mjpeg(img_bgr):
        ok, buf = cv2.imencode('.jpg', img_bgr, [int(cv2.IMWRITE_JPEG_QUALITY), jpeg_quality])
        if not ok: return None
        return (b'--frame\r\n'
                b'Content-Type: image/jpeg\r\n\r\n' +
                buf.tobytes() + b'\r\n')

    try:
        while True:
            frame_count += 1
            if use_external_frames:
                frame = frame_source()
                if frame is None:
                    continue
            else: 
                ok, frame = cap.read()
                if not ok: break

            H, W = frame.shape[:2]
            view = frame.copy()

            if roi_rect is None:
                rx, ry, rw, rh = 0, 0, W, H
            else:
                rx, ry, rw, rh = clamp_roi(*roi_rect, W=W, H=H)

            frame_roi = frame[ry:ry+rh, rx:rx+rw]

            # ---- YOLO detect ----
            results = model(frame_roi, conf=0.5, verbose=False)
            card_detected = False
            best_box = None

            if results and len(results[0].boxes) > 0:
                box = max(results[0].boxes, key=lambda b: float(b.conf))
                x1, y1, x2, y2 = map(int, box.xyxy[0].tolist())
                best_box = (x1, y1, x2, y2)
                card_detected = True

            # ---- logic ổn định ----
            if card_detected:
                stable += 1
                absence_cnt = 0
            else:
                stable = 0
                absence_cnt += 1
                if absence_cnt >= rearm_absence_frames:
                    armed = True
                    saved = False

            # ---- chụp ảnh ----
            if armed and card_detected and stable >= stable_frames and not saved:
                if return_crop and best_box:
                    x1, y1, x2, y2 = best_box
                    x1, y1 = max(0, x1), max(0, y1)
                    x2, y2 = min(rw, x2), min(rh, y2)
                    # captured = frame_roi[y1:y2, x1:x2]
                    captured = frame
                # else:
                #     captured = frame.copy()

                filepath = path_manager.get_captured_image_path("card", int(time.time()))
                cv2.imwrite(filepath, captured)
                captured_image = captured.copy()
                saved = True
                armed = False
                stable = 0
                absence_cnt = 0

                print(f"[INFO] Card captured: {filepath}")

                # TTS: Đọc thông báo khi card được capture
                try:
                    from card.card_tts import speak_card_captured
                    speak_card_captured('en')  # Đọc bằng tiếng Anh
                except Exception as e:
                    print(f"[WARN] Card TTS failed: {e}")

                try:
                    if camera_controller:
                        if not camera_controller.session_model.current_session:
                            sid = camera_controller.session_model.create_session()
                            print(f"Created new session: {sid}")
                        camera_controller.session_model.save_captured_image(filepath, "card")
                        camera_controller.check_and_trigger_ai_pipeline()
                except Exception as e:
                    print(f"[WARN] Save to session failed: {e}")

            if saved and captured_image is not None:
                yield bgr_to_mjpeg(captured_image)
            else:
                yield bgr_to_mjpeg(view)             

            # ---- hiển thị ----
            # display_frame = frame_roi.copy()
            # if best_box:
            #     x1, y1, x2, y2 = best_box
            #     cv2.rectangle(display_frame, (x1, y1), (x2, y2), (0, 255, 0), 2)

            # if saved and captured_image is not None:
            #     cv2.imshow("Card Captured", captured_image)
            # cv2.imshow("Card Detection", display_frame)

            # key = cv2.waitKey(1) & 0xFF
            # if key == 27:  # ESC
            #     break
    except GeneratorExit:
        pass
    finally:
        if cap is not None:
            cap.release()



    if frame_source is None: 
        cap.release()
        cv2.destroyAllWindows()

# if __name__ == "__main__":
#     detect_card_direct(yolo_model)
